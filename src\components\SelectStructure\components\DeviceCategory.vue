<template>
  <treeselect
    :options="deviceSystemList"
    no-children-text="无选择"
    placeholder="请选择设备系统分类"
    :normalizer="normalizer"
    v-model="id"
    no-options-text="正在加载..."
  ></treeselect>
</template>

<script>
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
// import { getDeviceCategoryList } from '@/api/project/deviceSystem'
import { getDeviceTypeList } from '../../../api/system/device'
export default {
  name: 'DeviceCategory',
  components: { Treeselect },
  model: {
    prop: 'categoryId',
    event: 'change'
  },
  props: {
    categoryId: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      id: this.categoryId,
      deviceSystemList: [],
      normalizer(node) {
        return {
          id: node.id,
          label: node.name,
          children: node.children
        }
      }
    }
  },
  watch: {
    id() {
      this.$emit('change', this.id)
    },
    categoryId() {
      this.id = this.categoryId ? this.categoryId : null
      this.$emit('change', this.id)
    }
  },
  mounted() {
    this.getDeviceSystemList()
  },
  methods: {
    getDeviceSystemList() {
      getDeviceTypeList({
        currPage: 1,
        pageSize: 2147483647,
        params: {}
      }).then(response => {
        if (response.success) {
          this.deviceSystemList = response.result
          this.deviceSystemList = this.treeListUtil(response.result)
        }
      })
    },
    treeListUtil(data, parentId) {
      const itemArr = []
      for (let i = 0; i < data.length; i++) {
        const node = data[i]
        if (node.parentId === parentId) {
          if (this.treeListUtil(data, node.id).length > 0) {
            node.children = this.treeListUtil(data, node.id)
          }
          itemArr.push(node)
        }
      }
      return itemArr
    }
  }
}
</script>

<style scoped>
</style>
