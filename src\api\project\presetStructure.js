import request from '@/utils/request'

// 保存预设结构图
export function saveCustomStructure(param) {
  return request({
    url: 'rest/presetStructure/save',
    method: 'post',
    data: param
  })
}

// 获取预设结构图
export function getCustomStructure(param) {
  return request({
    url: 'rest/presetStructure/get',
    method: 'post',
    data: param
  })
}

// 获取设备某个channel数据-遥测量
export function getDeviceData(param) {
  return request({
    url: 'rest/presetStructure/channelDataAny',
    method: 'post',
    data: param
  })
}

// 获取设备某个channel数据-遥信量
export function getDeviceStatus(param) {
  return request({
    url: 'rest/presetStructure/deviceRunStatus',
    method: 'post',
    data: param
  })
}

// 获取多通道曲线数据
export function getMultiChannelData(param) {
  return request({
    url: 'rest/presetStructure/dataMoreChannel',
    method: 'post',
    data: param
  })
}

// 删除预设结构图
export function deleteCustomStructure(param) {
  return request({
    url: 'rest/presetStructure/delete',
    method: 'post',
    data: param
  })
}

// 预设结构图列表
export function getCustomStructureList(param) {
  return request({
    url: 'rest/presetStructure/list',
    method: 'post',
    data: param
  })
}

// 根据code获取下一级
export function getCustomStructureListByParentCode(param) {
  return request({
    url: 'rest/presetStructure/queryNextByCode',
    method: 'post',
    data: param
  })
}

// 根据code获取下一级
export function getChannel(param) {
  return request({
    url: '/rest/channel/get',
    method: 'post',
    data: param
  })
}
