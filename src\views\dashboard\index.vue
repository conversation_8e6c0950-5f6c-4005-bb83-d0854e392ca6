<template>
  <div style="position: relative" class="system-dashboard">
    <dashboard-map ref="map" />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import DashboardMap from './components/dashboardMap'
export default {
  name: 'Dashboard',
  components: {
    DashboardMap
  },
  data() {
    return {
      projectList: null
    }
  },
  computed: {
    ...mapGetters(['name', 'avatar', 'roles'])
  },
  mounted() {
    this.$nextTick(() => {
      setTimeout(() => {
        this.handleResize()
      }, 200)
    })
  },
  methods: {
    handleResize() {
      // const dom = document.getElementsByClassName('app-main')[0]
      const appHeight = document.getElementById('app').clientHeight
      // document.getElementsByClassName('system-dashboard')[0].style.minHeight = '67.5rem'
      // document.getElementsByClassName('system-dashboard')[0].style.minWidth = '1920px'
      document.getElementsByClassName('system-dashboard')[0].style.height =
        appHeight - 49 + 'px'
      // document.getElementsByClassName('system-dashboard')[0].style.width = dom.clientWidth + 'px'
    }
  }
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.system-dashboard {
  position: absolute;
  margin-top: -71px;
  background: #ecf7ff;
  padding: 0;
}
.emptyGif {
  display: block;
  width: 45%;
  margin: 0 auto;
}

.dashboard-top {
  .pan-info-roles {
    font-size: 12px;
    font-weight: 700;
    color: #333;
    display: block;
  }
  .info-container {
    position: relative;
    //height: 150px;
    line-height: 30px;
    .display_name {
      font-size: 48px;
      line-height: 48px;
      color: #212121;
      position: absolute;
      top: 25px;
    }
  }
}
</style>
