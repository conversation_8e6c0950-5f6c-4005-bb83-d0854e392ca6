//地图图层
import SWMTS from 'ol/source/wmts';
import WMTS from 'ol/tilegrid/wmts';
import Map from 'ol/map';
import View from 'ol/view';
import Tile from 'ol/layer/tile';
import proj from 'ol/proj';
import XYZ from 'ol/source/xyz';
import OSM from 'ol/source/osm';
import ScaleLine from 'ol/control/scaleline';
import TileWMS from 'ol/source/tilewms';
import sVector from 'ol/source/vector';
import Vector from 'ol/layer/vector';
import Feature from 'ol/feature';
import Point from 'ol/geom/point';
import Style from 'ol/style/style';
import Fill from 'ol/style/fill';
import Stroke from 'ol/style/stroke';
import Icon from 'ol/style/icon';
import extent from 'ol/extent';
import GeoJSON from 'ol/format/geojson';
import Text from 'ol/style/text';
import Circle from 'ol/style/circle'
import ol from 'ol';

var dituService = function () {

    /* from 国家地理信息公共服务平台 http://www.tianditu.com/service/query.html

     全球影像地图服务（经纬度）    http://t0.tianditu.com/img_c/wmts
    全球影像注记服务（经纬度）    http://t0.tianditu.com/cia_c/wmts
    全球影像地图服务（墨卡托投影）   http://t0.tianditu.com/img_w/wmts
    全球影像注记服务（墨卡托投影）   http://t0.tianditu.com/cia_w/wmts

    全球矢量地图服务（经纬度）     http://t0.tianditu.com/vec_c/wmts
    全球矢量注记服务（经纬度）     http://t0.tianditu.com/cva_c/wmts
    全球矢量地图服务（墨卡托投影）    http://t0.tianditu.com/vec_w/wmts
    全球矢量注记服务（墨卡托投影）   http://t0.tianditu.com/cva_w/wmts

    全球地形晕渲地图服务（经纬度）     http://t0.tianditu.com/ter_c/wmts
    全球地形晕渲注记服务（经纬度）     http://t0.tianditu.com/cta_c/wmts
    全球地形晕渲地图服务（墨卡托投影） http://t0.tianditu.com/ter_w/wmts
    全球地形晕渲注记服务（墨卡托投影） http://t0.tianditu.com/cta_w/wmts
     */

    /*  重要说明： http://www.tianditu.com/service/query.html  <AUTHOR> 
 
     （1）天地图有7个服务节点，代码中没有固定使用哪个节点的服务，而是使用 Math.round(Math.random()*7)的方式随机决定从哪个节点请求服务，避免指定节点因故障等原因停止服务的风险。
     
     （2）天地图服务分经纬度和墨卡托投影两种类型，所以url中地图类型的投影方式应与参数 projection中保持一致(EPSG4326或EPSG3857)。
     
     （3）天地图切片服务共有18级，在计算分辨率时，最大计算到18级；经实验发现，若指定到19或更高级别，放大到对应级别后，地图出现空白。
     
     （4）WMTS方式加载的参数中，layer、matrixSet的可取值见下面【天地图地图类型及url】，format的取值固定为tiles, style参数为天地图服务默认的'style'。 */

    var tiandituBaseLayers = ["vec_w", "img_w", "ter_w", "cva_w", "cia_w", "cta_w", "img_c", "vec_c", "ter_c", "cia_c", "cva_c", "cta_c"];
    var tiandituVec = getTiandituBaseLayer("地图", tiandituBaseLayers[0]);
    var tiandituImg = getTiandituBaseLayer("影像", tiandituBaseLayers[1]);
    var tiandituTerra = getTiandituBaseLayer("地形", tiandituBaseLayers[2]);
    var tiandituAnno_vec = getTiandituBaseLayer("地图标注", tiandituBaseLayers[3], 1);
    //加载離綫地圖web 墨卡托平面坐标系，支持标准tms,谷歌tms
    var offLayer = {
        getOfflineLayer: function (layerType, layerName) {
            return new Tile({
                source: new XYZ({
                    tileUrlFunction: function (tileCoord) {
                        var x, y, z;
                        switch (layerType) {
                            case "TMS":
                                z = tileCoord[0];
                                x = tileCoord[1];
                                y = Math.pow(2, z) + tileCoord[2] - 1; break;
                            case 'Google':
                                z = tileCoord[0];
                                x = tileCoord[1];
                                y = - tileCoord[2];
                                break;
                            case "vector":
                                z = tileCoord[0];
                                x = tileCoord[1];
                                y = - tileCoord[2];
                                break;

                        }
                        return "http://localhost:8080/" + layerType + "/" + layerName + "/" + z + "/" + x + "/" + y + ".png";
                    }
                })

            });

        }

    };

    //openlayers 3 加载天地图 wmts服务
    //创建图层(WMTS方式)
    var tiandituWMTS = function (type, proj, opacity) {
        var projection = proj.get(proj);
        var projectionExtent = projection.getExtent();
        var size = extent.getWidth(projectionExtent) / 256;
        var resolutions = new Array(19);
        var matrixIds = new Array(19);
        for (var z = 1; z < 19; ++z) {
            // generate resolutions and matrixIds arrays for this WMTS
            resolutions[z] = size / Math.pow(2, z);
            matrixIds[z] = z;
        }

        var layer = new ol.layer.Tile({
            opacity: opacity,
            source: new SWMTS({
                attributions: 'Tiles © <a href="http://www.tianditu.com/service/info.html?sid=5292&type=info">天地图</a>',
                url: 'http://t' + Math.round(Math.random() * 7) + '.tianditu.com/' + type + '/wmts',
                layer: type.substr(0, 3),
                matrixSet: type.substring(4),
                format: 'tiles',//此爲固定
                projection: projection,
                tileGrid: new WMTS({
                    origin: extent.getTopLeft(projectionExtent),
                    resolutions: resolutions,
                    matrixIds: matrixIds
                }),
                style: 'default',
                wrapX: true
            })
        });
        layer.id = type;
        return layer;
    }


    //天地图图层
    var tiandituLayer = {
        //矢量底图
        vecLayer: tiandituVec,
        //影像
        imageLayer: tiandituImg,
        //地形
        terraLayer: tiandituTerra,
        //矢量标注图层
        annoLayer: tiandituAnno_vec,
        //wmts加载方式 function()
        wmtsLayer: tiandituWMTS

    }

    //mapbox图层
    var mapboxLayer = {
        //创建mapbox XYZ 切片（自定义样式)
        vecLayer: crtmapbox()
    }
    //百度在线切片加载
    var baiduMapLayer = {

        getLayer: function () {
            // 自定义分辨率和瓦片坐标系
            var resolutions = [];
            var maxZoom = 18;

            // 计算百度使用的分辨率
            for (var i = 0; i <= maxZoom; i++) {
                resolutions[i] = Math.pow(2, maxZoom - i);
            }
            var tilegrid = new ol.tilegrid.TileGrid({
                origin: [0, 0],    // 设置原点坐标
                resolutions: resolutions    // 设置分辨率
            });

            // 创建百度地图的数据源
            var baiduSource = new ol.source.TileImage({
                projection: 'EPSG:3857',
                tileGrid: tilegrid,
                tileUrlFunction: function (tileCoord, pixelRatio, proj) {
                    var z = tileCoord[0];
                    var x = tileCoord[1];
                    var y = tileCoord[2];

                    // 百度瓦片服务url将负数使用M前缀来标识
                    if (x < 0) {
                        x = 'M' + (-x);
                    }
                    if (y < 0) {
                        y = 'M' + (-y);
                    }

                    return "http://online" + Math.round(Math.random() * 4) + ".map.bdimg.com/onlinelabel/?qt=tile&x=" + x + "&y=" + y + "&z=" + z + "&styles=pl&udt=20160426&scaler=1&p=0";
                }
            });
            return new Tile({
                source: baiduSource
            });
        }
    }

    //创建图层(WMTS方式) mapbox的wmts 未请求到
    var ctrWMTS = function (type, proj, opacity) {
        var projection = proj.get(proj);
        var projectionExtent = projection.getExtent();
        var size = extent.getWidth(projectionExtent) / 512;
        var resolutions = new Array(21);
        var matrixIds = new Array(21);
        for (var z = 0; z <= 20; z++) {
            // generate resolutions and matrixIds arrays for this WMTS
            resolutions[z] = size / Math.pow(2, z);
            matrixIds[z] = z;
        }

        var layer = new Tile({
            opacity: opacity,
            source: new SWMTS({
                attributions: 'Tiles © <a href="https://blog.csdn.net/qq_26991807">suadi@author:dzj</a>',
                url: 'https://api.mapbox.com/styles/v1/dengzengjian/cjlxgdo6o4izx2sqrqigbgy4t/wmts?access_token=pk.eyJ1IjoiZGVuZ3plbmdqaWFuIiwiYSI6ImNqbGhnbWo1ZjFpOHEzd3V2Ynk1OG5vZHgifQ.16zy39I-tbQv3K6UnRk8Cw',
                // layer: 'cjlqs15s777sv2rmt5ybn6nh7',  
                matrixSet: 'EPSG:3857',
                format: 'image/png',
                projection: projection,
                tileGrid: new WMTS({
                    origin: extent.getTopLeft(projectionExtent),
                    resolutions: resolutions,
                    matrixIds: matrixIds
                }),
                // style: 'default',
                // wrapX: true
            })
        });
        // layer.id = type;
        return layer;
    }

    //创建wms图层geoserver发布的
    var crtwms = function (url, layers, projection) {
        var layers = new Tile({
            source: new TileWMS({
                url: url,//'http://localhost:8080/geoserver/gwc/service/wms', //http://**************:8093/geoserver/cBuilding/wms
                // extent:extent,   
                params: {
                    'LAYERS': layers,//'szdt:yanshenqujian',//postgis:procity,postgis:loccity 此处可以是单个图层名称，也可以是图层组名称，或多个图层名称  
                    'FORMAT': 'image/png',
                    'VERSION': '1.1.1',
                    'TILED': true,
                    'SRS': 'EPSG:12345',//设置了错了也没有用，默认为数据发布时的空间参考，比如我发布的时候用的是4326，但是依旧能显示
                },
                projection: projection//'EPSG:4326' , //决定了请求切片的投影坐标系    
                // serverType:'geoserver'    //服务器类型  
            }),
            zIndex: 2
        })
        return layers;
    }




    //封装天地图底图函数及标注图层
    function getTiandituBaseLayer(layername, layer, opacity) {
        return new Tile({
            opacity: opacity,
            title: layername,
            source: new XYZ({
                url: "http://t" + Math.round(Math.random() * 7) + ".tianditu.com/DataServer?tk=26f85c09ebd0e92982433b9a2eee8f51&T=" + layer + "&x={x}&y={y}&l={z}"
            })
        });
    };
    //创建mapbox的XYZ矢量图
    function crtmapbox() {
        var layer = new Tile({
            source: new XYZ({
                attributions: 'Tiles © <a href="https://blog.csdn.net/qq_26991807">suadi@author:dzj</a>',//cjlxgdo6o4izx2sqrqigbgy4t
                url: 'https://api.mapbox.com/styles/v1/dengzengjian/cjmhvbjkc3n1m2sp7kksazvy1/tiles/{z}/{x}/{y}?access_token=pk.eyJ1IjoiZGVuZ3plbmdqaWFuIiwiYSI6ImNqbGhnbWo1ZjFpOHEzd3V2Ynk1OG5vZHgifQ.16zy39I-tbQv3K6UnRk8Cw'
            }),
            zIndex: 2
        })
        return layer;
    }

    var drawpoint = function drawPoints(lng, lat, ICONSRC) {
        var pos = proj.transform([lng, lat], 'EPSG:4326', 'EPSG:3857');
        var iconFeature = new Feature({
            geometry: new Point(pos),

        });
        /* var stroke = new ol.style.Stroke({
            color: 'rgba(255,255,0,0.9)',
            width: 3
          }); */
        var iconStyle = new Style({
            image: new Icon(/** @type {olx.style.IconOptions} */({
                anchor: [0.5, 46],
                anchorXUnits: 'fraction',
                anchorYUnits: 'pixels',
                src: ICONSRC || 'https://openlayers.org/en/v4.6.5/examples/data/icon.png',
            }))
        });

        iconFeature.setStyle(iconStyle);

        var vectorSource = new sVector({
            features: [iconFeature]
        });

        var vectorLayer = new Vector({
            source: vectorSource,
            zIndex: 2
        });
        return iconFeature;

    }

    //通過geojson讀取feature創建圖層，将geojson定义为var 对象并引入js文件，更方便
    var creatLayerByJson = function (centerline_geojson, strokColor,annoName) {
        var style = new Style({
            image: new Circle({
                radius: 6,
                fill: new Fill({
                  color: "#000"
                })
              }),
            fill: new Fill({
                color: 'rgba(0, 0, 0, 0)'
            }),
            stroke: new Stroke({
                color: strokColor || 'rgba(1, 0, 0, 0)',//'#1757a5',
                width: 1
            }),
            text: new Text({
                font: '14px Calibri,sans-serif',
                fill: new Fill({
                    color: '#000'
                }),
                stroke: new Stroke({
                    color: '#fff',
                    width: 1
                })
            })
        })
        var vectorSource = new sVector({
            // features: (new GeoJSON()).readFeatures(centerline_geojson),
            url: centerline_geojson,//'https://openlayers.org/en/v4.6.5/examples/data/geojson/countries.geojson',
            format: new GeoJSON(),
        });
        var vectorLayer = new Vector({
            source: vectorSource,
            style: function(feature) {
                style.getText().setText(feature.get(annoName));
                return style;
              },
            name: "vectorLayer",
            zIndex: 2
        });
        vectorLayer.setVisible(true);
        return vectorLayer;
    }

    return { tiandituLayer: tiandituLayer, OfflineLayer: offLayer, baiduLayer: baiduMapLayer, mapboxLayer: mapboxLayer, wmts: ctrWMTS, wms: crtwms, drawPoints: drawpoint, jsonLayer: creatLayerByJson }

}
// dituService();
export default { dituService }
