<template>
  <div class="app-wrapper" :class="{hideSidebar:!sidebar.opened}">
    <sidebar class="sidebar-container" v-show="!hideLayout||(hideLayout && !isFull)"></sidebar>
    <navbar v-show="!hideLayout||(hideLayout && !isFull)"></navbar>
    <div class="main-container" style="  background: #f2f2f2;min-height: calc(100% - 50px)"
         :class="hideLayout && isFull ? 'active' : ''">
      <breadcrumb v-show="!hideLayout||(hideLayout && !isFull)" class="breadcrumb-container"></breadcrumb>
      <tags-view v-show="!hideLayout||(hideLayout && !isFull)"></tags-view>
      <app-main></app-main>
    </div>
  </div>
</template>

<script>
  import { Navbar, Sidebar, AppMain, TagsView } from './components'
  import Breadcrumb from '@/components/Breadcrumb'
  import { mapGetters } from 'vuex'

  export default {
    name: 'Layout',
    components: {
      Navbar,
      Sidebar,
      AppMain,
      TagsView,
      Breadcrumb
    },
    computed: {
      ...mapGetters([
        'hideLayout',
        'isFull'
      ]),
      sidebar() {
        return this.$store.state.app.sidebar
      }
    },
    mounted() {
      const me = this
      document.getElementsByClassName('app-main')[0].style.minHeight = document.getElementById('app').clientHeight - 120 + 'px'
      window.onresize = function windowResize() {
        if (me.hideLayout && me.isFull) {
          document.getElementsByClassName('app-main')[0].style.minHeight = document.getElementById('app').clientHeight + 'px'
        } else {
          document.getElementsByClassName('app-main')[0].style.minHeight = document.getElementById('app').clientHeight - 120 + 'px'
        }
      }
    }
  }
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
  @import "src/styles/mixin.scss";
  .app-wrapper {
    @include clearfix;
    /*position: relative;*/
    /*height: 100%;*/
    background: #304156;
    width: 100%;
    .active {
      margin-left: 0px !important;
    }
  }

  .breadcrumb-container {
    float: left;
  }
</style>
