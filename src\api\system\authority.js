import request from '@/utils/request'

export function getList(param) {
  return request({
    url: 'rest/privilege/list',
    method: 'post',
    data: param
  })
}

export function deleteAuth(param) {
  return request({
    url: 'rest/privilege/delete',
    method: 'post',
    data: param
  })
}

export function saveAuth(param) {
  return request({
    url: 'rest/privilege/save',
    method: 'post',
    data: param
  })
}
