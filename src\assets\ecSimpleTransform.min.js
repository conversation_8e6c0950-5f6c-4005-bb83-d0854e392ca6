!function(e,n){"object"==typeof exports&&"undefined"!=typeof module?n(exports):"function"==typeof define&&define.amd?define(["exports"],n):n((e="undefined"!=typeof globalThis?globalThis:e||self).ecSimpleTransform={})}(this,(function(e){"use strict";function n(e,n){if(!e)throw new Error(n)}function t(e,n){return e.hasOwnProperty(n)}var r={SUM:!0,COUNT:!0,FIRST:!0,AVERAGE:!0,Q1:!0,Q2:!0,Q3:!0,MIN:!0,MAX:!0},o={AVERAGE:["COUNT"]},i={Q1:!0,Q2:!0,Q3:!0},u={MEDIAN:"Q2"},a=function(){function e(e,n,t,r,o){this.collectionInfoList=[],this.gatheredValuesByGroup={},this.gatheredValuesNoGroup=[],this.needGatherValues=!1,this._collectionInfoMap={},this.method=t,this.name=r,this.index=e,this.indexInUpstream=n,this.needGatherValues=o}return e.prototype.addCollectionInfo=function(e){this._collectionInfoMap[e.method]=this.collectionInfoList.length,this.collectionInfoList.push(e)},e.prototype.getCollectionInfo=function(e){return this.collectionInfoList[this._collectionInfoMap[e]]},e.prototype.gatherValue=function(e,n,t){if(t=+t,e){if(null!=n){var r=n+"";(this.gatheredValuesByGroup[r]||(this.gatheredValuesByGroup[r]=[])).push(t)}}else this.gatheredValuesNoGroup.push(t)},e}(),l={type:"ecSimpleTransform:aggregate",transform:function(e){var r,u=e.upstream,l=e.config,p=function(e,t){var r,o=e.groupBy;null!=o&&n(r=t.getDimensionInfo(o),"Can not find dimension by `groupBy`: "+o);return r}(l,u),I=function(e,r,u){for(var l=e.resultDimensions,f=[],c=[],h=0,d=0;d<l.length;d++){var m=l[d],p=r.getDimensionInfo(m.from);n(p,"Can not find dimension by `from`: "+m.from);var v=m.method;n(u.index!==p.index||null==v,"Dimension "+p.name+' is the "groupBy" dimension, must not have any "method".');var I=s(v);n(I,"method is required");var g=null!=m.name?m.name:p.name,V=new a(f.length,p.index,I,g,t(i,I));f.push(V);var x=!1;if(t(o,I)){x=!0;for(var y=o[I],U=0;U<y.length;U++)V.addCollectionInfo({method:y[U],indexInLine:h++})}t(i,I)&&(x=!0),x&&c.push(V)}return{collectionDimInfoList:c,finalResultDimInfoList:f}}(l,u,p),g=I.finalResultDimInfoList,V=I.collectionDimInfoList;V.length&&(r=f(p,u,V,c,h));for(var x=0;x<V.length;x++){var y=V[x];y.__collectionResult=r,v(y.gatheredValuesNoGroup);var U=y.gatheredValuesByGroup;for(var G in U)t(U,G)&&v(U[G])}var L=f(p,u,g,d,m),M=[];for(x=0;x<g.length;x++)M.push(g[x].name);return{dimensions:M,data:L.outList}}};function f(e,n,r,o,i){var u,a=[];if(e){u={};for(var l=0,f=n.count();l<f;l++){var s=n.retrieveValue(l,e.index);if(null!=s){var c=s+"";if(t(u,c)){i(n,l,d=u[c],r,e,s)}else{var h=o(n,l,r,e,s);a.push(h),u[c]=h}}}}else{var d=o(n,0,r);a.push(d);for(l=1,f=n.count();l<f;l++)i(n,l,d,r)}return{mapByGroup:u,outList:a}}function s(e){if(null==e)return"FIRST";var o=e.toUpperCase();return o=t(u,o)?u[o]:o,n(t(r,o),"Illegal method "+e+"."),o}var c=function(e,n,t,r,o){for(var i=[],u=0;u<t.length;u++){for(var a=t[u],l=a.collectionInfoList,f=0;f<l.length;f++){var s=l[f];i[s.indexInLine]=+I[s.method](e,n,a,r,o)}if(a.needGatherValues){var c=e.retrieveValue(n,a.indexInUpstream);a.gatherValue(r,o,c)}}return i},h=function(e,n,t,r,o,i){for(var u=0;u<r.length;u++){for(var a=r[u],l=a.collectionInfoList,f=0;f<l.length;f++){var s=l[f],c=s.indexInLine;t[c]=+g[s.method](t[c],e,n,a,o,i)}if(a.needGatherValues){var h=e.retrieveValue(n,a.indexInUpstream);a.gatherValue(o,i,h)}}},d=function(e,n,t,r,o){for(var i=[],u=0;u<t.length;u++){var a=t[u],l=a.method;i[u]=p(r,a)?o:I[l](e,n,a,r,o)}return i},m=function(e,n,t,r,o,i){for(var u=0;u<r.length;u++){var a=r[u];if(!p(o,a)){var l=a.method;t[u]=g[l](t[u],e,n,a,o,i)}}};function p(e,n){return e&&n.indexInUpstream===e.index}function v(e){e.sort((function(e,n){return e-n}))}var I={SUM:function(){return 0},COUNT:function(){return 1},FIRST:function(e,n,t){return e.retrieveValue(n,t.indexInUpstream)},MIN:function(e,n,t){return e.retrieveValue(n,t.indexInUpstream)},MAX:function(e,n,t){return e.retrieveValue(n,t.indexInUpstream)},AVERAGE:function(e,n,t,r,o){var i=r?t.__collectionResult.mapByGroup[o+""]:t.__collectionResult.outList[0];return e.retrieveValue(n,t.indexInUpstream)/i[t.getCollectionInfo("COUNT").indexInLine]},Q1:function(e,n,t,r,o){return V(.25,t,r,o)},Q2:function(e,n,t,r,o){return V(.5,t,r,o)},Q3:function(e,n,t,r,o){return V(.75,t,r,o)}},g={SUM:function(e,n,t,r){return e+n.retrieveValue(t,r.indexInUpstream)},COUNT:function(e){return e+1},FIRST:function(e){return e},MIN:function(e,n,t,r){return Math.min(e,n.retrieveValue(t,r.indexInUpstream))},MAX:function(e,n,t,r){return Math.max(e,n.retrieveValue(t,r.indexInUpstream))},AVERAGE:function(e,n,t,r,o,i){var u=o?r.__collectionResult.mapByGroup[i+""]:r.__collectionResult.outList[0];return e+n.retrieveValue(t,r.indexInUpstream)/u[r.getCollectionInfo("COUNT").indexInLine]},Q1:function(e,n,t,r){return e},Q2:function(e,n,t,r){return e},Q3:function(e,n,t,r){return e}};function V(e,n,t,r){var o,i,u,a,l,f,s=t?n.gatheredValuesByGroup[r+""]:n.gatheredValuesNoGroup;return i=e,u=((o=s).length-1)*i+1,a=Math.floor(u),l=+o[a-1],(f=u-a)?l+f*(o[a]-l):l}e.aggregate=l,e.id={type:"ecSimpleTransform:id",transform:function(e){var n=e.upstream,t=e.config,r=t.dimensionIndex,o=t.dimensionName,i=n.cloneAllDimensionInfo();i[r]=o;for(var u=n.cloneRawData(),a=0,l=u.length;a<l;a++){u[a][r]=a}return{dimensions:i,data:u}}},Object.defineProperty(e,"__esModule",{value:!0})}));
