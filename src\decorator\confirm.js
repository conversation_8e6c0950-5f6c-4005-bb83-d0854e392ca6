/*
 * @Description
 * @Autor 朱俊
 * @Date 2020-03-20 18:32:54
 * @LastEditors 朱俊
 * @LastEditTime 2020-06-10 15:44:09
 */
import {
  Message,
  MessageBox
} from 'element-ui'

export const confirm = ({
                          placeholder,
                          list,
                          cancel,
                          success,
                          successMessage = '删除成功',
                          cancelMessage = '已取消删除',
                          showSuccessTip = true,
                          showErrorTip = true
                        }) => {
  if (!placeholder) placeholder = '此操作将永久删除, 是否继续?'
  return (target, name, descriptor) => {
    const oldFunction = descriptor.value
    descriptor.value = function fn(...args) {
      MessageBox.confirm(placeholder, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        oldFunction.apply(this, args).then(res => {
          showSuccessTip && Message({
            type: 'success',
            message: successMessage
          })
          list && this[list]()
          success && success(this, ...args)
        })
      }).catch(err => {
        console.log(err)
        showErrorTip && Message({
          type: 'info',
          message: cancelMessage
        })
        cancel && cancel(this)
      })
    }
    return descriptor
  }
}
