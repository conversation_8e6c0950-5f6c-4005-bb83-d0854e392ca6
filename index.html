<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="renderer" content="webkit">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
  <title>浦东大道地道全生命周期智慧运维</title>
</head>

<body>
  <!--<script type="text/javascript" src="http://webapi.amap.com/maps?v=1.3&key=6f547addbbc6f8bfeaf8e7841cf7dd92"></script>-->
  <!--<script type="text/javascript" src="http://webapi.amap.com/maps?v=1.3&key=6f547addbbc6f8bfeaf8e7841cf7dd92"></script>-->
  <!-- <script type="text/javascript" src="https://webapi.amap.com/maps?v=1.4.8&key=6f547addbbc6f8bfeaf8e7841cf7dd92&plugin=AMap.Autocomplete"></script> -->
  <script src="https://webapi.amap.com/loader.js"></script>
  <script type="text/javascript">
    window._AMapSecurityConfig = {
      securityJsCode: "d4058b0c6898502cc18f05e9314c3023",
    };
    AMapLoader.load({
      key: "bf7fefd5ff62dc100883d486ed59537b", //申请好的 Web 端开发 Key，首次调用 load 时必填
      version: "2.0", //指定要加载的 JS API 的版本，缺省时默认为 1.4.15
      plugins: ["AMap.Scale"], //需要使用的的插件列表，如比例尺'AMap.Scale'，支持添加多个如：['AMap.Scale','...','...']
      AMapUI: {
        //是否加载 AMapUI，缺省不加载
        version: "1.1", //AMapUI 版本
        plugins: ["overlay/SimpleMarker"], //需要加载的 AMapUI ui 插件
      },
      Loca: {
        //是否加载 Loca， 缺省不加载
        version: "2.0", //Loca 版本
      },
    })
      .then((AMap) => {
        var map = new AMap.Map("container"); //"container"为 <div> 容器的 id
        map.addControl(new AMap.Scale()); //添加比例尺组件到地图实例上
      })
      .catch((e) => {
        console.error(e); //加载错误提示
      });
  </script>
  <div id="app"></div>
</body>

</html>