import request from '@/utils/request'

// 分页查询测点组列表
export function getDeviceGroupList(param) {
  return request({
    url: 'rest/deviceGroup/list',
    method: 'post',
    data: param
  })
}
// 获取单条记录
export function getOneDeviceGroup(param) {
  return request({
    url: 'rest/deviceGroup/get',
    method: 'post',
    data: param
  })
}

// 删除单条记录
export function deleteDeviceGroup(param) {
  return request({
    url: 'rest/deviceGroup/delete',
    method: 'post',
    data: param
  })
}

// 保存测点组记录
export function saveDeviceGroup(param) {
  return request({
    url: 'rest/deviceGroup/save',
    method: 'post',
    data: param
  })
}

export function getDeviceTypeByGroup(param) {
  return request({
    url: 'rest/deviceGroup/getDeviceType',
    method: 'post',
    data: param
  })
}

// 获取某个时间点的测点组数据
export function getSpecialAnalysis(param) {
  return request({
    url: 'rest/deviceGroup/specialAnalysis',
    method: 'post',
    data: param
  })
}

export function getDeviceListByGroupCode(param) {
  return request({
    url: 'rest/deviceGroup/deviceListByGroupCode',
    method: 'post',
    data: param
  })
}
