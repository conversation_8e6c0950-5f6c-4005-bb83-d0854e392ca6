<!-- description -->
<template>
  <div class="dp-pannel-basic" v-if="isShow">
    <div class="pannel-body">
      <div class="pannel-header">
        <div class="pannel-title">
          <div class="title-icon">
            <div class="default-icon">
              <img :src="panelOption.icon" v-if="panelOption.icon">
              <icon-arrow v-else/>
            </div>
          </div>
          <span>{{ panelOption.title }}</span>
          <div class="close-btn" @click.stop="close">
            <i class="el-icon-close"></i>
          </div>
        </div>
        <div class="title-line">
          <div class="square"></div>
          <div class="line"></div>
          <div class="square"></div>
        </div>
      </div>
      <div class="panel-content">
        <slot/>
      </div>
    </div>
  </div>
</template>

<script>
  import IconArrow from './icon/IconArrow'
  import { mapGetters } from 'vuex'
  export default {
    name: 'PannelBasic',
    components: { IconArrow },
    directives: {},
    props: {
      visible: {
        type: Boolean,
        default: false
      },
      title: {
        type: String,
        default: '标题'
      },
      iconSrc: {
        type: String,
        default: ''
      }
    },
    data() {
      return {
        isShow: this.visible
      }
    },
    computed: {
      ...mapGetters(['panelOption'])
    },
    watch: {
      visible(val) {
        this.isShow = val
      }
    },
    created() {},
    mounted() {},
    methods: {
      close() {
        this.isShow = false
        this.$emit('update:visible', false)
        this.$emit('close')
      }
    }
  }
</script>

<style rel="stylesheet/scss" lang="scss">
.dp-pannel-basic {
  position: relative;
  min-width: 420px;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 100%), linear-gradient(168.53deg, rgba(234, 255, 254, 0.2) 0%, rgba(201, 229, 241, 0.2) 97.46%);
  border: 1.5px solid rgba(255, 255, 255, 0.7);
  border-radius: 12px;
  padding: 10px;
  .pannel-body {
    position: relative;
    width: 100%;
    background: radial-gradient(197.72% 261.75% at -2.6% -18.98%, rgba(233, 244, 255, 0.9) 0%, #FFFFFF 11.28%, rgba(255, 255, 255, 0.95) 80%, rgba(255, 255, 255, 0.76) 88.6%, rgba(226, 240, 255, 0.81) 100%);
    border-radius: 6px;
    height: 100%;
    padding: 10px;
    margin: 0;
    .pannel-header {
      width: 100%;
      height: 34px;
      margin-bottom: 12px;
      .pannel-title {
        height: 28px;
        font-size: 18px;
        display: flex;
        .close-btn {
          position: absolute;
          right: 10px;
          font-size: 20px;
          font-weight: bold;
          cursor: pointer;
        }
        .title-icon {
          .default-icon {
            margin-right: 6px;
            img {
              max-width: 18px !important;
            }
            //width: 8px;
            //height: 10px;
            //background: url("../../assets/dp/icon/icon-arrow.png");
          }
        }
      }
      .title-line {
        height: 6px;
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .line{
          border: 1px solid rgba(150, 152, 154, 1);
          width: calc(100% - 16px);
          height: 0;
        }
        .square {
          width: 4px;
          height: 4px;
          background:rgba(150, 152, 154, 1);
          transform: rotate(-45deg);;
        }
      }
    }
  }
}
</style>
