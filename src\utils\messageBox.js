import Vue from 'vue'

export function confirmPrevent(txt, customOptions) {
  const defaultOptions = {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }
  const options = { ...defaultOptions, ...customOptions }
  return new Promise((resolve, reject) => {
    Vue.prototype.$confirm(txt, '提示', options).then(() => {
      resolve()
    }).catch(() => {
      reject()
    })
  })
}
export function deletePrevent(txt, customOptions) {
  return confirmPrevent('是否确定删除记录？', customOptions)
}

export function fetchApiData(api, param = {}, key) {
  return new Promise(resolve => {
    api(param).then(res => {
      if (res.success) {
        res.result ? resolve(key ? res.result[key] : res.result) : resolve(null)
      }
    })
  })
}
