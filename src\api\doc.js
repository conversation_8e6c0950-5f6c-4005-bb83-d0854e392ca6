import request from '@/utils/request'

export function uploadDoc(param) {
  return request({
    url: 'rest/doc/uploadDoc',
    method: 'post',
    data: param
  })
}

export function getPicture(param) {
  return request({
    url: 'rest/doc/attachList',
    method: 'post',
    data: param
  })
}

export function docAttachList(param) {
  return request({
    url: 'rest/doc/attachList',
    method: 'post',
    data: param
  })
}
