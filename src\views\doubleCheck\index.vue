<!-- 事件二次确认 -->
<template>
  <div class="app-container double-check-container">
    <my-card title="事件二次确认">
      <div class="filter-container">
        <el-date-picker
          class="filter-item dateRange"
          style="display: inline-flex"
          v-model="chooseDate"
          type="datetimerange"
          range-separator="—-"
          value-format="yyyy-MM-dd HH:mm:ss"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        >
        </el-date-picker>
        <SelectEventType
          v-model="eventType"
          class="filter-item"
          :width="340"
          placeholder="事件类型"
          @selectChange="handeleEventClick"
        ></SelectEventType>
        <el-select
          class="filter-item"
          style="width: 200px"
          clearable
          v-model="listQuery.params.checkResult"
          placeholder="是否属实"
          @change="search"
        >
          <el-option label="属实" :value="1"></el-option>
          <el-option label="误报" :value="-1"></el-option>
        </el-select>
        <el-select
          class="filter-item"
          style="width: 200px"
          clearable
          v-model="listQuery.params.process"
          placeholder="请选择是否处理"
          @change="search"
        >
          <el-option label="未处理" :value="0"></el-option>
          <el-option label="已处理" :value="1"></el-option>
        </el-select>
        <el-select
          class="filter-item"
          style="width: 200px"
          clearable
          v-model="listQuery.params.userId"
          placeholder="请选择审核人员"
          @change="search"
        >
          <el-option
            v-for="item in userList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>

        <div style="float: right">
          <el-button
            class="filter-item"
            type="primary"
            v-waves
            icon="el-icon-search"
            @click="search"
          >查询</el-button
          >
          <el-button
            style="margin-left: 30px"
            class="filter-item"
            type="primary"
            v-waves
            icon="el-icon-refresh"
            @click="resetListQuery"
          >重置</el-button
          >
        </div>
      </div>
      <table-list
        show-index
        :data="dataList"
        :columns="columns"
        :list-loading="listLoading"
        :table-row-class-name="tableRowClassName"
        :total="total"
        :page-size="listQuery.pageSize"
        :page-num="listQuery.currPage"
        @handleSizeChange="handleSizeChange"
        @currentChange="currentChange"
      ></table-list>
    </my-card>
    <!-- 事件处理 -->
    <my-dialog
      ref="eventForm"
      :is-edit="IsEdit"
      :show-id="tableId"
      @saveSuccess="handleSaveSuccess"
    ></my-dialog>
  </div>
</template>

<script>
import tableList from './components/table/tableList.vue'
import MyDialog from './components/myDialog/index.vue'
import MyCard from '@/components/MyCard/index.vue'
import SelectDict from '@/components/SelectDict/index.vue'
import AutoUpload from '@/components/AutoUpload'
import { parseTime } from '@/utils'
import waves from '@/directive/waves'
import { confirmEmergencyApi, userApi } from '@/api/index.js'
import SelectEventType from '@/components/SelectEventType/index.vue'
import CreateMqtt from '@/utils/mqtt.js'

export default {
  name: 'DoubleCheck',
  components: {
    tableList,
    MyCard,
    AutoUpload,
    SelectDict,
    SelectEventType,
    MyDialog
  },
  directives: { waves },
  data() {
    return {
      dataList: [],
      listLoading: false,
      chooseDate: [],
      eventType: null,
      listQuery: {
        pageSize: 30,
        currPage: 1,
        sortRule: 'alertTime:desc',
        params: {
          startTime: '',
          endTime: '',
          category: null,
          type: null,
          subType: null,
          checkResult: null,
          process: null
        }
      },
      listQueryOriginal: {},
      columns: [
        {
          text: '事件类型',
          value: 'dllptSubType'
          // width: 150,
          // filter: getNameByCode,
          // filterParams: ['jialiu_mec_event_judge_type'],
          // render: (h, { row }) => {
          //   return h('div', [
          //     getNameByCode(row.eventType + '', 'jialiu_mec_event_judge_type')
          //   ])
          // }
        },
        {
          text: '发现时间',
          value: 'alertTime',
          filter: parseTime
        },
        {
          text: '识别点位',
          value: 'deviceName'
        },
        {
          text: '处理状态',
          // width: 140,
          value: 'status',
          formatter: val => {
            if (
              val.checkResult === '' ||
              val.checkResult === null ||
              val.checkResult === undefined
            ) {
              return '未处理'
            }
            return '已处理'
          }
        },
        {
          text: '是否属实',
          // width: 140,
          value: 'checkResult',
          formatter: val => {
            switch (val.checkResult) {
              case -1:
                return '误报'
              case 1:
                return '属实'
              default:
                return ''
            }
          }
        },
        {
          text: '审核人员',
          value: 'userName'
        },
        {
          text: '操作',
          type: 'textButton',
          // width: 140,
          list: this.operButton
        }
      ],
      total: 0,
      pointList: [], // 识别点位下拉框数据
      selectList: [
        {
          id: '1',
          name: '属实'
        },
        {
          id: '0',
          name: '误报'
        }
      ],
      dealList: [
        {
          id: '0',
          name: '进行中'
        },
        {
          id: '2',
          name: '已结束'
        }
      ],
      selectData: [
        {
          id: '1',
          name: '是'
        },
        {
          id: '0',
          name: '否'
        }
      ],
      // // 弹窗
      // eventvisible: false, // 人工上报弹窗显示标记
      // eventForm: {
      //   isTrue: '',
      //   isTrue2: '',
      //   isReported: '',
      //   categoryId: '',
      //   typeId: '',
      //   subTypeId: ''
      // },
      // eventLoading: false,
      IsEdit: false, // 单据操作状态：false--查看 true--编辑
      // eventRules: {
      //   isTrue: [{ required: true, message: '请选择', trigger: 'change' }],
      //   isReported: [{ required: true, message: '请选择', trigger: 'change' }],
      //   categoryId: [{ required: true, message: ' ', trigger: 'change' }],
      //   typeId: [{ required: true, message: ' ', trigger: 'change' }],
      //   subTypeId: [{ required: true, message: ' ', trigger: 'change' }]
      // },
      tableId: 1,
      PublicMqtt: null,
      userList: []
    }
  },
  computed: {},
  watch: {
    chooseDate(val) {
      if (val) {
        this.listQuery.params.startTime = this.chooseDate[0]
        this.listQuery.params.endTime = this.chooseDate[1]
        this.search()
      } else {
        this.listQuery.params.startTime = null
        this.listQuery.params.endTime = null
        this.search()
      }
    }
  },
  mounted() {
    this.listQueryOriginal = JSON.parse(JSON.stringify(this.listQuery))
    this.getList()
    this.startMqtt()
    this.getUserList()
  },
  methods: {
    handeleEventClick(val) {
      if (val) {
        this.listQuery.params = {
          category: val.category,
          type: val.type,
          subType: val.subType
        }
      } else {
        this.listQuery.params = {
          category: null,
          type: null,
          subType: null
        }
      }

      this.search()
    },
    // 获取所有列表
    getList() {
      confirmEmergencyApi.list(this.listQuery).then(res => {
        console.log('列表', res)
        if (res.success) {
          this.dataList = res.result.list
          this.total = res.result.total
        }
      })
    },
    search() {
      this.listQuery.currPage = 1
      this.getList()
    },
    resetListQuery() {
      this.listQuery = JSON.parse(JSON.stringify(this.listQueryOriginal))
      this.search()
    },
    handleSizeChange(val) {
      this.listQuery.pageSize = val
      this.getList()
    },
    currentChange(val) {
      this.listQuery.currPage = val
      this.getList()
    },
    // 表格操作按鈕
    operButton(val) {
      if (
        val.checkResult === undefined ||
        val.checkResult === null ||
        val.checkResult === ''
      ) {
        return [{ class: 'icon-z', value: '处理', click: this.dealEvent }]
      }
      return [{ class: 'icon-z', value: '查看', click: this.viewOne }]
    },
    tableRowClassName({ row }) {
      if (row.checkResult === undefined) {
        return 'red'
      } else {
        return 'green'
      }
    },
    viewOne(val) {
      console.log(val, 'res.val')
      this.IsEdit = false
      this.tableId = val.row.id
      this.$refs.eventForm.openDialog(val.row.id)
    },
    dealEvent(val) {
      this.IsEdit = true
      this.tableId = val.row.id
      this.$refs.eventForm.openDialog(val.row.id)
    },
    handleSaveSuccess() {
      this.getList()
    },
    startMqtt() {
      // 设置订阅地址
      if (this.PublicMqtt === null) {
        this.PublicMqtt = new CreateMqtt('alert')
        // 初始化mqtt
        this.PublicMqtt.init()
        // 链接mqtt
        this.PublicMqtt.link()
        // isSendToUe.value = false
        this.PublicMqtt.client.on('message', (topic, message) => {
          const mqttMessage = JSON.parse(JSON.parse(message.toString()))
          console.log(mqttMessage, '返回的数据')
        })
      }
    },
    unsubscribe() {
      // 如果页面并没有初始化MQTT，无需取消订阅
      if (this.PublicMqtt) {
        this.PublicMqtt.unsubscribes()
        this.PublicMqtt.over()
        this.PublicMqtt = null
      }
    },
    getUserList() {
      userApi.list({ params: {}}).then(res => {
        console.log('用户列表', res)
        if (res.success) {
          this.userList = res.result.list
        }
      })
    }
  }
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.double-check-container {
  /deep/.mxCard .titleSpan {
    margin-left: -30px;
  }
  .filter-container {
    margin-left: 40px;
  }
  .label-name {
    font-size: 14px;
    position: relative;
    top: -5px;
  }
  .vue-treeselect {
    height: 32px;
  }
  .imgCol {
    img {
      width: 148px;
      height: 148px;
      margin-right: 10px;
    }
  }
  .radio-group {
    margin-left: 50px;
    width: 350px;
    display: flex;
    justify-content: center;
    text-align: center;
    flex-direction: column;
  }
}
.btnDiv {
  margin-top: 20px;
  text-align: center;
}
</style>
<style>
.green {
  color: green;
}
.red {
  color: #ff0000;
}
</style>
