<!--字典管理-->
<template>
  <div class="app-container dicDiv">
    <my-card title="字典管理">
      <el-button class="filter-item addButton" type="primary" @click="addDic" v-waves icon="el-icon-circle-plus-outline">添加</el-button>
      <tree-table :data="data" :columns="columns" :list-loading="listLoading" border></tree-table>
    </my-card>

    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible">
      <el-form :rules="rules" ref="dicForm" :model="dicForm" label-position="center" size="small" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="字典名称" prop="name">
              <el-input type="text" class="filter-item" placeholder="请输入字典名称" v-model="dicForm.name" maxlength="20">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="字典编码" prop="code">
              <el-input type="text" class="filter-item" placeholder="请输入字典编码" v-model="dicForm.code" maxlength="20">
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="saveDic" :loading="loading" v-waves>提交</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  /* eslint-disable no-undef */

  import TreeTable from '@/components/TreeTable'
  import MyCard from '@/components/MyCard'
  import waves from '@/directive/waves'
  import { getDicList, deleteDic, saveDic } from '@/api/system/dictionary'

  export default {
    name: 'Dictionary',
    components: {
      TreeTable, MyCard
    },
    directives: {
      waves
    },
    data() {
      return {
        dialogFormVisible: false, // 弹出框显示判断
        dialogStatus: 'create',
        textMap: {
          update: '编辑',
          create: '新建'
        },
        authList: [], // 字典数组
        loading: false, // 按钮重复提交判断
        listLoading: false,
        rules: {
          name: [{ required: true, message: '请填写字典名称' }],
          code: [{ required: true, message: '请填写字典编码' }]
        },
        dicForm: {
          id: '',
          parentId: '',
          name: '',
          code: ''
        },
        columns: [
          {
            text: '字典名称',
            value: 'name'
          },
          {
            text: '字典编码',
            value: 'code'
          },
          {
            text: '操作',
            type: 'iconButton',
            width: 120,
            list: this.operButton
          }
        ],
        data: []
      }
    },
    mounted() {
      this.getList()
    },
    methods: {
      // 获取所有字典
      getList: function() {
        this.listLoading = true
        getDicList({}).then(response => {
          if (response.success) {
            // this.data = this.treeListUtil(response.result)
            this.data = response.result
          }
          this.listLoading = false
        })
      },
      // 表格操作按鈕
      operButton(val) {
        const temp = []
        if (!val.parentId) {
          temp.push({ class: 'iconxinzeng', value: '添加', click: this.addChild })
        }
        temp.push({ class: 'iconxiugai', value: '修改', click: this.updateDic })
        if (!val.children || val.children.length === 0) {
          temp.push({ class: 'iconshanchu1', value: '删除', click: this.deleteDic })
        }
        return temp
      },
      // 更新字典
      updateDic(val) {
        this.dialogStatus = 'update'
        this.dicForm.id = val.row.id
        this.dicForm.parentId = val.row.parentId
        this.dicForm.name = val.row.name
        this.dicForm.code = val.row.code
        this.dialogFormVisible = true
      },
      // 添加子节点
      addChild(val) {
        this.dicForm = {
          id: '',
          parentId: val.row.id,
          name: '',
          code: ''
        }
        this.dialogFormVisible = true
      },
      // 删除字典
      deleteDic(val) {
        this.$confirm('是否确定删除该记录?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.loading = true
          deleteDic({ id: val.row.id }).then(response => {
            if (response.success) {
              this.data.splice(this.data.findIndex(item => item.id === val.row.id), 1)
              this.$message.success('删除成功')
            } else {
              this.$message.error(response.message)
            }
            this.loading = false
          })
        })
      },
      reset() { // 清空
        this.dicForm.id = ''
        this.dicForm.parentId = ''
        this.$refs['dicForm'].resetFields()
      },
      // 新增字典
      addDic() {
        this.dialogStatus = 'create'
        this.dicForm = {
          id: '',
          parentId: '',
          name: '',
          code: ''
        }
        this.dialogFormVisible = true
      },
      setDgData(item, result) {
        item.name = result.name
        item.code = result.code
      },
      // 保存字典
      saveDic() {
        this.$refs['dicForm'].validate((valid) => {
          if (valid) {
            this.loading = true
            saveDic(this.dicForm).then(response => {
              if (response.success) {
                this.$message.success('保存成功')
                // 操作数结构
                response.result.children = []
                // 判断是更新还是添加
                if (this.dicForm.id !== '') {
                  const findVal = this.data.find(item => {
                    return item.id === response.result.id
                  })
                  this.setDgData(findVal, response.result)
                } else {
                  this.data.push(response.result)
                }
                this.dialogFormVisible = false
                this.reset()
              } else {
                this.$message.error(response.message)
              }
              this.loading = false
            })
          }
        })
      }
    }
  }
</script>

<style rel="stylesheet/scss" lang="scss">
  .dicDiv {
    .addButton {
      float: right;
      margin-right: 10px;
      margin-bottom: 10px;
    }
  }
</style>
