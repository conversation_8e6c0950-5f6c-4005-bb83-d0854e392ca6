<!-- 分值环形图 -->
<template>
  <div class="dashboard-pie-chart">
    <div class="content">
      <div class="pieBox" :id="chartId"></div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PieChart',
  /* 参数
    target: 目标id
    frontColor：前景色
    backColor：背景色
    value： 数值
    label：标签
  */
  props: {
    title: {
      type: String,
      default: ''
    },
    titleColor: {
      type: String,
      default: 'rgba(58, 236, 253, 1)'
    },
    subText: {
      type: String,
      default: ''
    },
    target: {
      type: String,
      default: ''
    },
    frontColor: {
      type: String,
      default: '#20BD17'
    },
    backColor: {
      type: String,
      default: '#39CBCB'
    },
    value: {
      type: Array,
      default: null
    },
    total: {
      type: Number,
      default: null
    },
    label: {
      type: String,
      default: 'name'
    },
    result: {
      type: String,
      default: ''
    },
    valueSuffix: {
      type: String,
      default: ''
    },
    textColors: {
      type: Array,
      default: () => ['RGBA(0, 209, 218, 1)', 'RGBA(228, 202, 95, 1)']
    },
    colors: {
      type: Array,
      default: () => {
        return [
          'rgba(7, 111, 149, 1)',
          'rgba(255, 215, 92, 1)',
          'rgba(90, 220, 156, 1)',
          'rgba(71, 168, 251, 1)'
        ]
      }
    }
  },
  data() {
    const self = this
    // const chartBg = require('@/assets/dashboard/panel/chart-bg.png')
    return {
      chartId: '',
      pieChart: null,
      pieOption: {
        color: this.colors,
        legend: {
          right: 0,
          top: 'middle',
          data: this.value.map(item => item.name),
          orient: 'vertical',
          icon: 'circle',
          // selectedMode: false,
          textStyle: {
            color: 'rgba(51, 97, 143, 1)',
            fontSize: 13,
            rich: {
              name: {
                fontSize: 16,
                lineHeight: 20
              },
              value: {
                fontSize: 16,
                color: 'black',
                fontWeight: 'bold'
              },
              level1: {
                fontSize: 16,
                color: self.textColors[0]
              },
              level2: {
                fontSize: 16,
                color: self.textColors[1]
              },
              level3: {
                fontSize: 16,
                color: self.textColors[2]
              },
              level4: {
                fontSize: 16,
                color: self.textColors[3]
              },
              suffix: {
                fontSize: 14,
                color: this.backColor
              }
            }
          }
          // formatter: function (name) {
          //   const item = self.value.find(item => item.name === name)
          //   const val = item.value
          //   const index = self.value.indexOf(item)
          //   return '{level' + (index + 1) + '|' + name + '}'
          // }
        },
        grid: {
          left: 0
        },
        title: {
          // show: false,
          textAlign: 'center',
          textVerticalAlign: 'middle',
          text: self.total,
          subtext: self.subText,
          itemGap: 5,
          textStyle: {
            fontSize: 20,
            fontWeight: 'bold',
            color: 'rgba(7, 111, 149, 1)'
          },
          subtextStyle: {
            fontSize: 12,
            fontWeight: 'normal',
            color: 'rgba(7, 111, 149, 1)'
          },
          top: '40%',
          left: '28%'
        },
        tooltip: {
          trigger: 'item'
        },
        series: [{
          hoverAnimation: false,
          type: 'pie',
          center: ['30%', '50%'],
          radius: ['60%', '88%'],
          roseType: 'radius',
          label: {
            normal: {
              show: false,
              color: 'grey',
              lineHeight: 20,
              formatter: function(param) {
                return '{name|' + self.label + '}' + '\n{value|' + self.result + '}'
              },
              position: 'center',
              rich: {
                name: {
                  fontSize: 16,
                  lineHeight: 40,
                  color: 'black'
                },
                value: {
                  fontSize: 16,
                  color: 'black',
                  fontWeight: 'bold'
                },
                suffix: {
                  fontSize: 14,
                  color: this.backColor
                }
              }
            }
          },
          labelLine: {
            normal: {
              show: false
            }
          },
          itemStyle: {
            normal: {
              // borderColor: 'RGBA(30, 63, 73, 1)',
              borderWidth: 2
            }
          },
          data: []
        }]
      }
    }
  },
  watch: {
    value(newValue) {
      if (this.value === 0) {
        this.pieOption.series[0].data = [
          { value: 0, name: 'grade' },
          { value: 1, name: 'rest' }
        ]
        this.pieChart.setOption(this.pieOption, true)
        return
      }
      this.pieOption.series[0].data = newValue.map((item, index) => {
        return {
          ...item,
          itemStyle: {
            color: this.colors[index]
          }
        }
      })
      // this.pieOption.series[0].data[0].value = newValue
      // this.pieOption.series[0].data[1].value = this.total - newValue
      this.pieChart.setOption(this.pieOption, true)
    }
  },
  created() {
    this.chartId = 'pie_' + Math.floor(Math.random() * 999) * Math.floor(Math.random() * 999)
  },
  mounted() {
    this.pieChart = this.$echarts.init(document.getElementById(this.chartId))
    // if (this.value === 0) {
    //   this.pieOption.series[0].data = [
    //     { value: 0, name: 'grade' },
    //     { value: 1, name: 'rest' }
    //   ]
    // }
    this.pieOption.series[0].data = this.value.map((item, index) => {
      return {
        ...item,
        itemStyle: {
          color: this.colors[index]
        }
      }
    })
    this.pieChart.setOption(this.pieOption, true)
  }
}
</script>

<style lang='scss'>
.dashboard-pie-chart {
  width: 100%;
  height: 120px;
  position: relative;

  .content {
    position: relative;
    z-index: 10;
    width: 100%;
    height: 100%;
    padding: 0;

    .pieBox {
      width: 100%;
      height: 100%;
    }
  }

  p.placeholder {
    font-size: 14px;
    position: absolute;
    z-index: 3;
    top: 25%;
    left: 25%;
    color: #A0A0A0;
    padding: 0;
  }
}
</style>
