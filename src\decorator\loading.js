/*
 * @Description
 * @Autor 朱俊
 * @Date 2020-03-21 11:06:50
 * @LastEditors 朱俊
 * @LastEditTime 2020-10-12 14:54:53
 */

export const loading = (loadingName) => {
  return (target, name, descriptor) => {
    const timeout = 180000 // 设置默认超时3分钟
    const oldValue = descriptor.value
    descriptor.value = function fn(...args) {
      // 当loadingName为函数时 处理
      let realLoadingName = loadingName
      if (loadingName.constructor === Function) {
        realLoadingName = loadingName(this, ...args)
      }
      this[realLoadingName] = true
      // 需要监听如果超时 将再次置为false
      let timer = setTimeout(() => {
        this[realLoadingName] = false
        this.$message({
          type: 'warning',
          message: '请求超时'
        })
      }, timeout)
      oldValue.call(this, ...args).then(res => {
        this[realLoadingName] = false
        clearTimeout(timer)
        timer = null
      })
    }
    return descriptor
  }
}

export const validLoading = (ref, loadingName, options) => {
  return (target, name, descriptor) => {
    const timeout = 180000
    const oldValue = descriptor.value
    descriptor.value = function fn(...args) {
      this.$refs[ref].validate((valid) => {
        if (valid) {
          // 执行前处理
          if (options && options.before) {
            options.before(this, ...args)
          }
          // 当loadingName为函数时 处理
          let realLoadingName = loadingName
          if (loadingName.constructor === Function) {
            realLoadingName = loadingName(this, ...args)
          }
          this[realLoadingName] = true
          // 需要监听如果超时 将再次置为false
          let timer = setTimeout(() => {
            this[realLoadingName] = false
            this.$message({
              type: 'warning',
              message: '请求超时'
            })
          }, timeout)
          oldValue.call(this, ...args).then(res => { // apply->call apply要求传参格式为数组 by zangyunfei
            this[realLoadingName] = false
            clearTimeout(timer)
            timer = null
            // 执行后处理
            if (options && options.after) {
              options.after(this, ...args)
            }
          })
        }
      })
    }
    return descriptor
  }
}
