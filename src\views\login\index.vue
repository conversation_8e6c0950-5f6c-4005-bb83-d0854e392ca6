<template>
  <div class="login-container">
    <el-form
      class="login-form"
      auto-complete="on"
      :model="loginForm"
      :rules="loginRules"
      ref="loginForm"
      label-position="left"
    >
      <div class="title-container">
        <h3 class="title">登录</h3>
      </div>
      <el-form-item prop="username">
        <span class="svg-container svg-container_login">
          <i class="icon iconfont">&#xe603;</i>
        </span>
        <el-input
          name="username"
          type="text"
          v-model="loginForm.username"
          auto-complete="on"
          placeholder="请输入用户名"
        />
      </el-form-item>

      <el-form-item prop="password">
        <span class="svg-container">
          <i class="icon iconfont">&#xe614;</i>
        </span>
        <el-input
          name="password"
          :type="passwordType"
          @keyup.enter.native="handleLogin"
          v-model="loginForm.password"
          auto-complete="on"
          placeholder="请输入密码"
        />
        <span class="show-pwd" @click="showPwd">
          <i class="icon iconfont">&#xe60d;</i>
        </span>
      </el-form-item>
      <el-form-item style="position: relative;" prop="verifycode">
        <el-input
          v-model="loginForm.verifycode"
          placeholder="请输入验证码"
          class="identifyinput"
          @keyup.enter.native="handleLogin"
        ></el-input>
        <div class="identifybox">
          <div @click="refreshCode">
            <s-identify :identify-code="identifyCode"></s-identify>
          </div>
        </div>
      </el-form-item>

      <el-button
        type="primary"
        :loading="loading"
        @click.native.prevent="handleLogin"
      >登录</el-button
      >
    </el-form>

    <el-dialog
      :title="$t('login.thirdparty')"
      :visible.sync="showDialog"
      append-to-body
    >
      {{ $t('login.thirdpartyTips') }}
      <br />
      <br />
      <br />
      <social-sign />
    </el-dialog>
  </div>
</template>

<script>
import SocialSign from './socialsignin'
import SIdentify from './identify'
import { singleProjectList } from '@/api/project/project'

export default {
  name: 'Login',
  components: { SocialSign, SIdentify },
  data() {
    const validateUsername = (rule, value, callback) => {
      if (value.length === 0) {
        callback(new Error('用户名不能为空'))
      } else {
        callback()
      }
    }
    const validatePassword = (rule, value, callback) => {
      if (value.length === 0) {
        callback(new Error('密码不能为空'))
      } else {
        callback()
      }
    }
    const validateVerifycode = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入验证码'))
      } else if (value !== this.identifyCode) {
        callback(new Error('验证码不正确!'))
      } else {
        callback()
      }
    }
    return {
      identifyCode: '',
      identifyCodes: '234567890',
      projectIds: [],
      loginForm: {
        username: '',
        password: ''
      },
      loginRules: {
        username: [
          { required: true, trigger: 'blur', validator: validateUsername }
        ],
        password: [
          { required: true, trigger: 'blur', validator: validatePassword }
        ],
        verifycode: [
          { required: true, trigger: 'blur', validator: validateVerifycode }
        ]
      },
      passwordType: 'password',
      loading: false,
      showDialog: false
    }
  },
  created() {
    this.refreshCode()
  },
  methods: {
    showPwd() {
      if (this.passwordType === 'password') {
        this.passwordType = ''
      } else {
        this.passwordType = 'password'
      }
    },
    handleLogin() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.loading = true
          this.$store
            .dispatch('LoginByUsernameNew', this.loginForm)
            .then(() => {
              this.loading = false
              singleProjectList({}).then(res => {
                if (res.success) {
                  this.projectList = res.result
                  const arrIds = []
                  this.projectList.forEach(e => {
                    arrIds.push(e.id)
                  })
                  this.projectIds = arrIds
                  this.$storage.setStorage('BpProjectIdArr', this.projectIds)
                  if (res.result && res.result.length > 0) {
                    if (res.result[0].name.indexOf('边坡') !== -1) {
                      this.$storage.setStorage('myProjType', 'bp')
                    } else if (res.result[0].name.indexOf('S') !== -1) {
                      this.$storage.setStorage('myProjType', 's6')
                    }
                  }

                  this.$store.dispatch('setFileUrl')
                  this.$store.dispatch('setDictMap')
                  // this.$store.dispatch('setMessageNum')
                  if (this.projectList.length === 1) {
                    this.$storage.setStorage('project', res.result[0])
                    this.$router.push({
                      path: '/projectDash/projectDashboard'
                    })
                  } else {
                    this.$router.push({ path: '/' })
                  }
                }
              })
            })
            .catch(() => {
              this.loading = false
            })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    afterQRScan() {
      // const hash = window.location.hash.slice(1)
      // const hashObj = getQueryObject(hash)
      // const originUrl = window.location.origin
      // history.replaceState({}, '', originUrl)
      // const codeMap = {
      //   wechat: 'code',
      //   tencent: 'code'
      // }
      // const codeName = hashObj[codeMap[this.auth_type]]
      // if (!codeName) {
      //   alert('第三方登录失败')
      // } else {
      //   this.$store.dispatch('LoginByThirdparty', codeName).then(() => {
      //     this.$router.push({ path: '/' })
      //   })
      // }
    },
    randomNum(min, max) {
      return Math.floor(Math.random() * (max - min) + min)
    },
    refreshCode() {
      this.identifyCode = ''
      this.makeCode(this.identifyCodes, 4)
    },
    makeCode(o, l) {
      for (let i = 0; i < l; i++) {
        this.identifyCode += this.identifyCodes[
          this.randomNum(0, this.identifyCodes.length)
        ]
      }
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss">
$bg: #2d3a4b;
$light_gray: #eee;

/* reset element-ui css */
.login-container {
  .identifybox {
    position: absolute;
    height: 47px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 0px;
    overflow: hidden;
    line-height: 47px;
    padding: 0 10px;
    top: 0px;
    right: 0px;
  }
  background: url('../../assets/login-bg.png');
  .el-input {
    display: inline-block;
    height: 47px;
    width: 85%;
    input {
      background: transparent;
      border: 0px;
      -webkit-appearance: none;
      border-radius: 0px;
      padding: 12px 5px 12px 15px;
      color: $light_gray;
      height: 47px;
      &:-webkit-autofill {
        -webkit-box-shadow: 0 0 0px 1000px $bg inset !important;
        -webkit-text-fill-color: #fff !important;
      }
    }
  }
  .el-form-item {
    border: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(0, 0, 0, 0.1);
    border-radius: 5px;
    color: #454545;
  }
}
</style>

<style rel="stylesheet/scss" lang="scss" scoped>
$bg: #2d3a4b;
$dark_gray: #889aa4;
$light_gray: #eee;

.login-container {
  position: fixed;
  height: 100%;
  width: 100%;
  background-color: $bg;
  .login-form {
    position: absolute;
    left: 0;
    right: 0;
    width: 520px;
    padding: 35px 35px 15px 35px;
    margin: 120px auto;
    .el-button {
      width: 100%;
      margin-bottom: 30px;
    }
  }
  .tips {
    font-size: 14px;
    color: #fff;
    margin-bottom: 10px;
    span {
      &:first-of-type {
        margin-right: 16px;
      }
    }
  }
  .svg-container {
    padding: 6px 5px 6px 15px;
    color: $dark_gray;
    vertical-align: middle;
    width: 30px;
    display: inline-block;
    &_login {
      font-size: 20px;
    }
  }
  .title-container {
    position: relative;
    .title {
      font-size: 26px;
      font-weight: 400;
      color: $light_gray;
      margin: 0px auto 40px auto;
      text-align: center;
      font-weight: bold;
    }
    .set-language {
      color: #fff;
      position: absolute;
      top: 5px;
      right: 0px;
    }
  }
  .show-pwd {
    position: absolute;
    right: 10px;
    top: 7px;
    font-size: 16px;
    color: $dark_gray;
    cursor: pointer;
    user-select: none;
  }
  .thirdparty-button {
    position: absolute;
    right: 35px;
    bottom: 28px;
  }
}
</style>
