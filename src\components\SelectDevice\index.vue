<template>
  <el-row>
    <el-input
      v-if="isInput"
      readonly
      v-model="dataName"
      @click.native="selectStructure"
    />
    <el-button
      :type="buttonType"
      :icon="buttonIcon"
      v-if="isButton"
      @click="selectStructure">
      {{ buttonText }}
    </el-button>
    <el-dialog
      :title="dialogTitle"
      :visible.sync="visible"
      @close="cancel"
      :close-on-click-modal="false"
      width="1200px"
      append-to-body
      class="selectStructure">
      <div class="filter-container">
        <select-device-type v-model="listQuery.deviceTypeId"
                            :disable-branch-nodes="disabledCategoryBranchNodes"
                            :disabled="deviceCategoryDisabled"
                            @change="search"
                            style="width: 200px;"
                            class="filter-item">
        </select-device-type>
        <el-input
          v-model="listQuery.name"
          placeholder="请输入关键字"
          class="filter-item"
          style="width: 150px"
        />
        <el-button
          class="filter-item"
          type="primary"
          v-waves
          icon="el-icon-search"
          @click="search">
          查询
        </el-button>
        <el-button
          class="filter-item"
          type="info"
          icon="el-icon-refresh"
          @click="searchReset">重置</el-button>
      </div>
      <el-row>
        <el-col :span="11">
          <el-card>
            <div slot="header" class="clearfix">
              <span>{{ titleMap[type] }}列表</span>
              <el-button
                type="primary"
                v-waves
                v-if="multiple&&showAllSelectButton"
                @click="pageChoose"
                class="allButton">单页全选</el-button>
              <el-button
                style=""
                type="primary"
                v-waves
                @click="allChoose"
                v-if="multiple&&showAllSelectButton">所有全选</el-button>
            </div>
            <table-list
              :columns="columns"
              class="dataTable"
              :page-size="pageQuery.pageSize"
              :page-num="pageQuery.currPage"
              v-if="showList"
              :total="total"
              :data="list"
              small-pagination
              @currentChange="currentChange"
              style="width: 100%;"></table-list>
          </el-card>
        </el-col>
        <el-col :span="2">
          <div class="arrowDiv">
            <div><i class="el-icon-back"></i></div>
            <div><i class="el-icon-back"></i></div>
          </div>
        </el-col>
        <el-col :span="11">
          <el-card>
            <div slot="header" class="clearfix">
              <span>已选{{ titleMap[type] }}</span>
            </div>
            <table-list :columns="selectedColumns"
                        v-if="showCurrList"
                        class="dataTable"
                        :page-size="10"
                        :page-num="currPage"
                        @currentChange="selectedChange"
                        :data="currList"
                        :list-loading="currListLoading"
                        small-pagination
                        :total="selectedTotal"></table-list>
          </el-card>
        </el-col>
      </el-row>
      <div slot="footer"
           class="dialog-footer">
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" @click="confirm">确定</el-button>
      </div>
    </el-dialog>
  </el-row>

</template>

<script>
  import waves from '@/directive/waves'
  import TableList from '@/components/table/tableList.vue'
  import { getDeviceGroupList } from '@/api/project/deviceGroup'
  import { mapGetters } from 'vuex'
  import SelectDeviceType from '@/components/SelectDeviceType/index'
  import { getDeviceList } from '../../api/project/device'
  /**
   * 四码合一选择设施/设备/设备组
   * @module  SelectStructure
   */
  export default {
    name: 'SelectDevice',
    /**
     * @prop {Component} SelectManageUnit 选择管理单元组件
     * @prop {Component} SelectUnitCode 选择编码组件
     * @prop {Component} TableList 表格组件
     * @prop {Component} DeviceCategory 选择设备分类组件
     * @prop {Component} StructureCategory 选择设施类型组件
     */
    components: {
      SelectDeviceType,
      TableList
    },
    directives: { waves },
    model: {
      prop: 'dataList',
      event: 'change'
    },
    /**
     * Props 父组件传值
     * @prop {Array} dataList v-model绑定值，选中的设施/设备/设备组列表
     * @prop {Boolean} multiple 是否多选
     * @prop {String} type 类型('device', 'structure', 'deviceGroup')
     * @prop {Boolean} isReady 是否可以操作
     * @prop {String} positionId 限定管理单元Id
     * @prop {Boolean} isSameType 是否限定为同一类设施/设备
     * @prop {Boolean} isSameManageUnit 是否限定为同一个管理单元下的设施/设备
     * @prop {Number} professionId 专业类型Id
     * @prop {String} inputType 选择样式('input', 'button')
     * @prop {String} buttonText 按钮文字，inputType为'button'时生效
     * @prop {String} buttonType 按钮类型，inputType为'button'时生效
     * @prop {String} buttonIcon 按钮图标，inputType为'button'时生效
     * @prop {Boolean} disabledCategoryBranchNodes 是否禁用设施/设备分类非子节点，
     * @prop {Boolean} deviceCategoryDisabled 是否禁用设备系统分类选择
     * @prop {Boolean} isMonitorSystem 是否是测点设备
     * @prop {Number} deviceSystemId 设备分类Id
     * @prop {Boolean} inspectRelation 是否是巡检关联
     * @prop {Boolean} showAllSelectButton 是否显示单页全选和所有全选按钮
     */
    props: {
      dataList: {
        type: Array,
        default: () => []
      },
      multiple: {
        type: Boolean,
        default: false
      },
      type: {
        type: String,
        default: 'device'
      },
      isReady: {
        type: Boolean,
        default: true
      },
      isSameType: {
        type: Boolean,
        default: false
      },
      inputType: {
        type: String,
        default: 'input'
      },
      buttonText: {
        type: String,
        default: '选择'
      },
      buttonType: {
        type: String,
        default: ''
      },
      buttonIcon: {
        type: String,
        default: ''
      },
      disabledCategoryBranchNodes: {
        type: Boolean,
        default: true
      },
      isMonitorSystem: {
        type: Boolean,
        default: false
      },
      deviceCategoryDisabled: {
        type: Boolean,
        default: false
      },
      deviceSystemId: {
        type: Number,
        default: null
      },
      deviceTypeId: {
        type: Number,
        default: null
      },
      showAllSelectButton: {
        type: Boolean,
        default: true
      }
    },
    data() {
      return {
        innerDeviceOrStructure: '',
        allButtonShow: true,
        titleMap: {
          structure: '设施',
          device: '设备',
          deviceGroup: '设备组'
        },
        // 列表接口映射
        postMap: {
          device: getDeviceList,
          deviceGroup: getDeviceGroupList
        },
        // 类型属性字段
        typeMap: {
          structure: 'type',
          device: 'deviceTypeId',
          deviceGroup: 'type'
        },
        visible: false,
        listQuery: {
          name: '',
          position: '',
          mileageValue: '',
          range: '',
          deviceTypeId: null
        },
        pageQuery: {
          pageSize: 10,
          currPage: 1
        },
        listQueryParam: {
          manageUnitId: '',
          roomId: '',
          type: 'device',
          pageSize: 10,
          currPage: 1,
          smallTypeId: '',
          bigTypeId: '',
          name: ''
        },
        columns: [
          {
            text: '名称',
            value: 'name'
          },
          {
            text: '操作',
            type: 'button',
            width: 100,
            list: this.operButton
          }
        ],
        mileageNum: '',
        list: [],
        allList: [],
        total: 0,
        showList: true,
        showCurrList: true,
        currentIndex: 0,
        selectedList: this.dataList.slice(0),
        currList: this.dataList.slice(0),
        dataName: this.dataList.map(item => item.name).join('、'),
        selectedTotal: 0,
        currPage: 1,
        selectedColumns: [
          {
            text: '名称',
            value: 'name'
          },
          {
            text: '操作',
            type: 'button',
            width: 50,
            list: this.selectedButton
          }
        ],
        currListLoading: false
      }
    },
    computed: {
      ...mapGetters(['dictMap', 'projectParam']),
      isDevice() {
        return this.type === 'device' || this.innerDeviceOrStructure === 'device'
      },
      isInput() {
        return this.inputType === 'input'
      },
      isButton() {
        return this.inputType === 'button'
      },
      dialogTitle() {
        return `选择${this.type ? this.titleMap[this.type] : '设施/设备'}`
      }
    },
    watch: {
      dataList() {
        this.selectedList = this.dataList.slice(0)
        this.updateList()
      },
      selectedList() {
        this.selectedTotal = this.selectedList.length
        const nameList = this.selectedList.map(item => item.name)
        this.dataName = nameList.join('、')
        this.updateList(this.currPage)
      },
      type() {
        this.search()
      },
      visible() {
        this.search()
      }
    },
    mounted() {
      // this.searchReset()
      this.search()
    },
    methods: {
      // 表格操作按鈕
      operButton(val) {
        const temp = [{ class: 'icon-z', value: '选择', click: this.addStructure, type: 'text' }]
        return !val.isSelected ? temp : []
      },
      selectedButton() {
        return [{ class: 'icon-z', value: '删除', click: this.deleteStructure, type: 'text' }]
      },
      /**
       * 添加一行选中数据
       * @function addStructure
       * @param val {Object} - 选择一行数据, eg. { row: { id: xx, ...} ...}
       * @param [list] {Array} - 数据源，不传为左侧当前页列表
       */
      addStructure(val, list) {
        const id = val.row.id
        const type = this.type || this.innerDeviceOrStructure
        if (this.selectedList.some(item => item.id === id)) {
          // this.$message.info('不能重复选择')
          return
        }
        const multiPleCheckFail = !this.multiple && this.selectedList.length === 1
        if (multiPleCheckFail) {
          // this.$message.info('只能选择一个')
          this.list.forEach(item => {
            item.isSelected = item.id === id
          })
          this.selectedList = [val.row]
          return
        }
        const fieldName = this.typeMap[type]
        const isNotEmpty = this.selectedList.length > 0
        const sameTypeCheckFail = isNotEmpty && (val.row[fieldName] !== this.selectedList[0][fieldName] && this.isSameType)
        if (sameTypeCheckFail) {
          this.$message.info(`只能选择同一类型${this.titleMap[type]}`)
          return
        }
        this.showList = false
        let structure = null
        if (this.isArray(list)) {
          structure = list.find(item => item.id === id)
        } else {
          structure = this.list.find(item => item.id === id)
        }
        structure.isSelected = true
        structure.deviceOrStructure = type
        this.selectedList.push(structure)
        const itemInCurrentList = this.list.find(item => item.id === id)
        if (itemInCurrentList) {
          itemInCurrentList.isSelected = true
        }
        this.$nextTick(() => {
          this.showList = true
        })
      },
      /**
       * 删除一行数据
       * @function functionName
       * @param val {Object} - 选中列表中的一行数据, eg. { row: { id: xx, ...} ...}
       */
      deleteStructure(val) {
        const target = this.selectedList.find(item => item.id === val.row.id)
        const index = this.selectedList.indexOf(target)
        this.selectedList.splice(index, 1)
        const temp = this.list.find(item => item.id === val.row.id)
        if (temp) {
          temp.isSelected = false
        }
      },
      cancel() {
        this.searchReset()
        this.visible = false
      },
      confirm() {
        this.$emit('change', this.selectedList)
        this.searchReset()
        this.visible = false
      },
      /**
       * 搜索条件重置
       * @function searchReset
       */
      searchReset() {
        this.listQuery = {
          position: '',
          mileageValue: '',
          range: '',
          pageSize: 10,
          currPage: 1,
          deviceSystemId: null
        }
        this.selectedList = this.dataList.slice(0)
        this.list = []
        this.total = 0
        this.listQuery.professionId = null
        // this.innerDeviceOrStructure = ''
        // this.type = ''
      },
      /**
       * 查询
       * @function search
       */
      async search() {
        if (!this.type && !this.innerDeviceOrStructure) {
          // this.$message.error('请先选择专业')
          this.list = []
          this.total = 0
        }
        this.listQuery.currPage = 1
        this.listQueryParam.currPage = 1
        //
        this.list = await this.getList()
      },
      // page, 是否分页
      getList(listQuery, page = true) {
        listQuery = listQuery || this.listQuery
        const pageQuery = this.pageQuery
        return new Promise(async resolve => {
          // 内部类型数据优先
          const targetType = this.innerDeviceOrStructure ? this.innerDeviceOrStructure : this.type
          const method = this.postMap[targetType]
          if (!method) {
            return
          }
          if (this.isMonitorSystem) {
            listQuery.monitor = true
          }
          if (this.deviceTypeId) {
            this.listQuery.deviceTypeId = this.deviceTypeId
          }
          if (!page) {
            pageQuery.pageSize = null
          }
          const response = await method({ ...pageQuery, params: { ...listQuery }})
          if (response.success && response.result) {
            const list = response.result.list
            list.forEach(item => {
              item.isSelected = this.selectedList.some(structure => structure.id === item.id)
              if (this.isDeviceGroup) {
                item.name = item.name || item.code
              }
              item.location = item.ex7 || item.mileage || item.startMileage
              if (item.ex1 && item.ex2) {
                item.clock = `${item.ex1}~${item.ex2}`
              } else {
                item.clock = item.ex1 || item.ex2 || ''
              }
            })
            this.total = response.result.total
            resolve(list)
          } else {
            this.total = 0
            resolve([])
          }
        })
      },
      async currentChange(val) {
        this.pageQuery.currPage = val
        this.listQueryParam.currPage = val
        this.list = await this.getList()
      },
      selectedChange(val) {
        this.currPage = val
        this.updateList(val)
      },
      updateList(val = 1) {
        if (val > 1 && ((val - 1) * 10) >= this.selectedTotal) {
          this.currPage = --val
        }
        this.showCurrList = false
        const endIndex = (val * 10 > this.selectedTotal) ? this.selectedTotal : val * 10
        this.currList = this.selectedList.slice(10 * (val - 1), endIndex)
        this.$nextTick(() => {
          this.showCurrList = true
        })
      },
      selectStructure() {
        if (!this.isReady) {
          this.$emit('isReady', false)
          return
        }
        this.visible = true
      },
      /**
       * 当前页全选
       * @function pageChoose
       */
      pageChoose() {
        this.list.forEach(item => {
          this.addStructure({ row: { ...item }})
        })
      },
      /**
       * 全选所有
       * @function allChoose
       */
      async allChoose() {
        this.currListLoading = true
        const allListQuery = { ...this.listQuery }
        allListQuery.pageSize = null
        const allList = await this.getList(allListQuery, false)
        allList.forEach(item => {
          this.addStructure({ row: { ...item }}, allList)
        })
        this.currListLoading = false
      },
      isArray(o) {
        return Object.prototype.toString.call(o) === '[object Array]'
      }
    }
  }
</script>

<style lang="scss">
  .selectStructure {
    .el-dialog {
      width: 80%;
    }
    .arrowDiv{
      padding: 10px;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      height: 100%;
      div {
        height: 50%;
        cursor: pointer;
        font-size: 20px;
        line-height: 80px;
        &:nth-child(2) {
          transform: rotate(180deg);
        }
      }
    }
    .el-input__icon.el-input__validateIcon.el-icon-circle-close,
    .el-input__icon.el-input__validateIcon.el-icon-circle-check{
      display: none;
    }
    .allButton{
      margin-left: 190px;
    }
  }
</style>
