const screen = {
  state: {
    activeMenu: 'Home',
    subMenu: '',
    markers: [],
    focusModelId: null,
    panelOption: { // 弹窗配置
      title: '弹窗',
      icon: '',
      component: '',
      data: null,
      show: false
    },
    showMonitor: false // 交通监测、风速展示
  },
  mutations: {
    SET_ACTIVE_MENU: (state, menu) => {
      state.activeMenu = menu
    },
    SET_SUB_MENU: (state, subMenu) => {
      state.subMenu = subMenu
    },
    SET_MARKERS: (state, markers) => {
      state.markers = markers
    },
    SET_FOCUS_MODEL_ID: (state, modelId) => {
      state.focusModelId = modelId
    },
    SET_PANEL_OPTION: (state, option) => {
      state.panelOption = option
    },
    SET_SHOW_MONITOR: (state, isShow) => {
      state.showMonitor = isShow
    }
  },
  actions: {
    setActiveMenu({ commit }, menu) {
      commit('SET_ACTIVE_MENU', menu)
    },
    setMarkers({ commit }, markers) {
      commit('SET_MARKERS', markers)
    },
    setFocusModelId({ commit }, modelId) {
      commit('SET_FOCUS_MODEL_ID', modelId)
    },
    setPanelOption({ commit }, option) {
      commit('SET_PANEL_OPTION', option)
    },
    setShowMonitor({ commit }, isShow) {
      commit('SET_SHOW_MONITOR', isShow)
    }
  }
}

export default screen
