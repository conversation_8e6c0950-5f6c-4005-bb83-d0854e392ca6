<!-- 自动上传组件 -->
<template>
  <el-form-item :label="label">
    <picture-card-list
      v-if="isView"
      is-view
      :file-list="picList">
    </picture-card-list>
    <el-upload
      v-else
      name="attach"
      :file-list="picList"
      :auto-upload="true"
      :action="action"
      :headers="headers"
      :data="uploadParam"
      :on-success="handleImgSuccess"
      :on-remove="handleImgRemove"
      :list-type="listType"
      :limit="limit"
      :on-exceed="handleExceed"
      :accept="accept"
      multiple>
      <i class="el-icon-plus avatar-uploader-icon"></i>
    </el-upload>
  </el-form-item>
</template>

<script>
import { getToken } from '@/utils/auth'
import { uploadDoc } from '@/api/doc'
import { mapGetters } from 'vuex'
import PictureCardList from '../PictureCardList/PictureCardList'
import { getPicture } from '../../api/system/device'

export default {
  name: 'AutoUpload',
  components: { PictureCardList },
  directives: {},
  model: {
    prop: 'docId',
    event: 'change'
  },
  props: {
    label: {
      type: String,
      default: '图片'
    },
    docId: {
      type: Number,
      default: null
    },
    fromAlert: {
      type: Boolean,
      default: false
    },
    isView: {
      type: Boolean,
      default: false
    },
    limit: {
      type: Number,
      default: null
    },
    listType: {
      type: String,
      default: 'picture-card'
    },
    accept: {
      type: String,
      default: '.jpg,.jpeg,.png,.gif,.bmp,.JPG,.JPEG,.PNG,.GIF,.BMP'
    }
  },
  data() {
    return {
      action: this.myUrl + 'rest/doc/uploadDoc',
      headers: {
        'x-auth-token': getToken()
      },
      uploadParam: { docId: '', delIds: [], docCategoryId: '', docCategoryCode: '', projectId: '' },
      picList: [],
      myDocId: this.docId
    }
  },
  computed: {
    ...mapGetters(['fileUrl'])
  },
  watch: {
    docId(val) {
      this.getImage()
    }
  },
  created() {},
  mounted() {
    this.getImage()
  },
  methods: {
    getImage(id) {
      if (!this.docId || !this.isView) {
        return
      }
      getPicture({ docId: this.docId }).then(res => {
        if (res.success) {
          this.picList = res.result.map(item => {
            item.url = `${this.fileUrl}/img/${item.fileName}`
            return item
          })
        }
      })
    },
    handleExceed() {
      this.$message.warning('最大上传数量：' + this.limit)
    },

    handleImgSuccess(res) {
      if (res.success) {
        this.$message.success(`${this.label}上传成功`)
        if (!this.docId) {
          this.myDocId = res.result.id
          this.uploadParam.docId = res.result.id
          this.$emit('change', res.result.id)
        }
        // document.getElementsByClassName('editDraggable')[0].style.background = `url(${this.backgroundUrl}) round`
      }
    },
    handleImgRemove(val) {
      // 新上传的imageId
      const formData = new FormData()
      formData.append('docId', this.myDocId)
      formData.append('delIds', [val.id])
      uploadDoc(formData).then(res => {
        if (res.success) {
          this.$message.success('删除成功')
        }
      })
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss">
</style>
