
const views = {
  state: {
    viewer: null,
    currentSceneList: [],
    allSceneList: [],
    viewPointList: {},
    sceneInfo: null,
    initPosition: null,
    cameraInfo: ''
  },
  mutations: {
    SET_VIEWER: (state, viewer) => {
      state.viewer = viewer
    },
    SET_SCENE_List: (state, currentSceneList) => {
      state.currentSceneList = currentSceneList
    },
    SET_ALL_SCENE_List: (state, allSceneList) => {
      state.allSceneList = allSceneList
    },
    SET_VIEW_POINT_LIST: (state, viewPointList) => {
      state.viewPointList = viewPointList
    },
    GET_SCENEINFO: (state, sceneInfo) => {
      state.sceneInfo = sceneInfo
    },
    GET_INIT_POSITION: (state, initPosition) => {
      state.initPosition = initPosition
    },
    FLY_TO_MODEL: (state, modelId) => {
      const model = state.viewer.getModelById(modelId)
      model && state.viewer.flyToModel(model)
    },
    SET_CAMERA_INFO: (state, cameraInfo) => {
      state.cameraInfo = { ...cameraInfo }
    },
    SET_CAMERA: (state, cameraInfo) => {
      const info = cameraInfo || state.cameraInfo
      state.viewer.flyToPositionTarget(info.position, info.lookAt)
    }
  },
  actions: {
    setViewer({ commit }, viewer) {
      commit('SET_VIEWER', viewer)
    },
    setSceneList({ commit }, currentSceneList) {
      commit('SET_SCENE_List', currentSceneList)
    },
    setAllSceneList({ commit }, allSceneList) {
      commit('SET_ALL_SCENE_List', allSceneList)
    },
    setViewPointList({ commit }, viewPointList) {
      commit('SET_VIEW_POINT_LIST', viewPointList)
    },
    getInitPosition({ commit }, initPosition) {
      commit('GET_INIT_POSITION', initPosition)
    },
    getSceneInfo({ commit }, sceneInfo) {
      commit('GET_SCENEINFO', sceneInfo)
    },
    flyToModel({ commit }, modelId) {
      commit('FLY_TO_MODEL', modelId)
    },
    setCameraInfo({ commit }, cameraInfo) {
      commit('SET_CAMERA_INFO', cameraInfo)
    },
    setCamera({ commit }, cameraInfo) {
      commit('SET_CAMERA', cameraInfo)
    }
  }
}

export default views
