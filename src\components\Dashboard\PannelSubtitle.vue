<!-- description -->
<template>
  <div class="panel-subtitle">
    <div class="title-icon">
      <div class="default-icon">
        <icon-arrow/>
      </div>
    </div>
    <span style="margin-top:10px;">{{ title }}</span>
  </div>
</template>

<script>
  import IconArrow from './icon/IconArrow'
  export default {
    name: 'PanelSubtitle',
    components: { IconArrow },
    directives: {},
    props: {
      title: {
        type: String,
        default: '标题'
      }
    },
    data() {
      return {}
    },
    computed: {},
    created() {},
    mounted() {},
    methods: {}
  }
</script>

<style rel="stylesheet/scss" lang="scss">
.panel-subtitle {
  height: 28px;
  font-size: 1rem;
  display: flex;
  margin-top: .5rem;
  letter-spacing: 1px;
  .title-icon {
    .default-icon {
      margin-right: 6px;
      margin-top:10px;
      //width: 8px;
      //height: 10px;
      //background: url("../../assets/dp/icon/icon-arrow.png");
    }
  }
}
</style>
