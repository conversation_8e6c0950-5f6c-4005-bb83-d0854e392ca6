import request from '@/utils/request'

export function getDeviceList(param) {
  return request({
    url: 'rest/device/list',
    method: 'post',
    data: param
  })
}

export function deleteDevice(param) {
  return request({
    url: 'rest/device/delete',
    method: 'post',
    data: param
  })
}

export function saveDevice(param) {
  return request({
    url: 'rest/device/save',
    method: 'post',
    data: param
  })
}

export function getOneDevice(param) {
  return request({
    url: 'rest/device/getDevice',
    method: 'post',
    data: param
  })
}

export function getDevice(param) {
  return request({
    url: 'rest/device/getDevice',
    method: 'post',
    data: param
  })
}

export function getDevices(param) {
  return request({
    url: 'rest/device/getDevices',
    method: 'post',
    data: param
  })
}

// 获取测点实时数据
export function channelData(param) {
  return request({
    url: 'rest/data/channelData',
    method: 'post',
    data: param
  })
}

// 获取测点实时数据 /rest/tableRecordExt/get
export function tableRecordExtGet(param) {
  return request({
    url: 'rest/tableRecordExt/get',
    method: 'post',
    data: param
  })
}

// rest/device/count
export function count(param) {
  return request({
    url: 'rest/device/count',
    method: 'post',
    data: param
  })
}

export function searchDevices(param) {
  return request({
    url: 'rest/device/deviceList',
    method: 'post',
    data: param
  })
}

export function getRemark(param) {
  return request({
    url: 'rest/deviceGroup/getRemark',
    method: 'post',
    data: param
  })
}

// 获取测点数据完整性
export function completenessStatistics(param) {
  return request({
    url: 'rest/data/completenessStatistics',
    method: 'post',
    data: param
  })
}

export function getDeviceChannels(param) {
  return request({
    url: 'rest/device/channels',
    method: 'post',
    data: param
  })
}
