import request from '@/utils/request'

export function getManufacturerList(param) {
  return request({
    url: 'rest/manufacturer/list',
    method: 'post',
    data: param
  })
}

export function deleteManufacturer(param) {
  return request({
    url: 'rest/manufacturer/delete',
    method: 'post',
    data: param
  })
}

export function saveManufacturer(param) {
  return request({
    url: 'rest/manufacturer/save',
    method: 'post',
    data: param
  })
}

export function getManufacturerListByDeviceTypeId(param) {
  return request({
    url: 'rest/manufacturer/manufacturerList',
    method: 'post',
    data: param
  })
}
