import mqtt from 'mqtt'

const connection = {
  // url: 'tech.suitbim.com/mqtt', // 连接地址
  clean: true, // 保留会话
  connectTimeout: 4000, // 超时时间
  reconnectPeriod: 4000, // 重连时间间隔
  // 认证信息
  clientId: 'qjsd' + Math.round(Math.random() * 10000) + Math.round(Math.random() * 10000),
  username: 'mqtt_username',
  password: 'mqtt_password'
}

function MqttConnection(url) {
  this.url = url
  this.client = this.createConnection(url)
  this.client.on('connect', () => {
    console.log('mqtt connect')
  })
  this.client.on('close', () => {
    console.log('mqtt close')
  })
}

Object.defineProperty(MqttConnection, 'client', {
  configurable: false,
  get: () => {
    return this.client
  }
})

MqttConnection.prototype.createConnection = function() {
  const url = this.url
  try {
    console.log('mqtt.connect success')
    return mqtt.connect(url, connection)
  } catch (error) {
    console.log('mqtt.connect error', error)
    return null
  }
}

MqttConnection.prototype.doSubscribe = function(subscription) {
  if (!this.client) {
    return false
  }
  const { topic } = subscription
  this.client.subscribe(topic, (error, res) => {
    if (error) {
      console.log('Subscribe to topics error', error)
      return
    }
    console.log('Subscribe to topics res', res)
  })
}

export default MqttConnection
