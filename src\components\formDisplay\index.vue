<template>
  <div class="FormDisplayDiv">
    <el-row class="row">
      <el-col class="flex contentDiv" v-bind:style="{width:currentWidth}" v-for="(item,index) in formData" :key="index">
        <div class="leftDiv">
          {{ item.name }}
        </div>
        <div class="rightDiv">
          {{ item.value }}
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
  export default {
    name: 'FormDisplay',
    props: {
      displayNum: {
        type: Number,
        default: 2
      },
      formData: {
        type: Array,
        default: () => []
      }
    },
    data() {
      return {
        currentWidth: null
      }
    },
    watch: {
      displayNum(val) {
        this.currentWidth = 100 / val + '%'
      },
      formData(val) {
        // 取余
        this.getRemainder(val)
      }
    },
    mounted() {
      this.currentWidth = 100 / this.displayNum + '%'
      this.getRemainder(this.formData)
    },
    beforeD<PERSON>roy() {
    },
    methods: {
      getRemainder(val) {
        const temp = val.length % this.displayNum
        if (temp > 0) {
          for (let i = 0; i < this.displayNum - temp; i++) {
            this.formData.push({ name: '', value: '' })
          }
          this.$forceUpdate()
        }
      }
    }
  }
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
  .FormDisplayDiv {
    .row {
      border-top: 1px solid #ccc;
      border-left: 1px solid #ccc;
      border-right: 1px solid #ccc;
      .contentDiv {
        border-bottom: 1px solid #ccc;
        .leftDiv {
          height: 40px;
          line-height: 40px;
          text-align: center;
          width: 200px;
          background: #FEEADF;
        }
        .rightDiv {
          height: 40px;
          line-height: 40px;
          text-align: center;
          width: 100%
        }
      }
    }
  }
</style>
