import request from '@/utils/request'

// 项目角色权限配置
export function configProjectRole(param) {
  return request({
    url: 'rest/projectPrivilege/configProjectRole',
    method: 'post',
    data: param
  })
}

// 项目角色分页查询
export function projectRoleList(param) {
  return request({
    url: 'rest/projectPrivilege/projectRoleList',
    method: 'post',
    data: param
  })
}

// 维护项目角色
export function saveProjectRole(param) {
  return request({
    url: 'rest/projectPrivilege/saveProjectRole',
    method: 'post',
    data: param
  })
}

// 项目角色权限清单
export function rolePrivileges(param) {
  return request({
    url: 'rest/projectPrivilege/rolePrivileges',
    method: 'post',
    data: param
  })
}

// 删除项目角色
export function deleteProjectRole(param) {
  return request({
    url: 'rest/projectPrivilege/deleteProjectRole',
    method: 'post',
    data: param
  })
}
