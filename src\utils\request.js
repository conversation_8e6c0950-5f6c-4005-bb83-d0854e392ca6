import axios from 'axios'
import { Message } from 'element-ui'
import store from '@/store'
import { getToken } from '@/utils/auth'
import Vue from 'vue'
import myStroage from './sessionStorage'
// create an axios instance
const service = axios.create({
  timeout: 0 // request timeout
})

// const needLoadingRequestCount = 0

// function showLoading() {
//   if (needLoadingRequestCount === 0) {
//     store.dispatch('setLoading', true)
//   }
//   needLoadingRequestCount++
// }

// function hideLoading() {
//   if (needLoadingRequestCount <= 0) return
//   needLoadingRequestCount--
//   if (needLoadingRequestCount === 0) {
//     store.dispatch('setLoading', false)
//   }
// }
// 判断数据类型是否为formdata
function isFormData(v) {
  return Object.prototype.toString.call(v) === '[object FormData]'
}

// 请求前拦截
service.interceptors.request.use(config => {
  if (myStroage.getStorage('project') && config.data) {
    if (isFormData(config.data) && !config.data.has('projectId')) {
      // 当为formdata类型
      config.data.set('projectId', JSON.parse(myStroage.getStorage('project')).id)
    } else if (!config.data.projectId) {
      // 当为普通的对象时
      config.data.projectId = JSON.parse(myStroage.getStorage('project')).id
      if (config.data.params) {
        config.data.params.projectId = config.data.projectId
      }
    }
  }
  // showLoading()
  if (config.type === 'demo') {
    config.baseURL = Vue.prototype.demoUrl
  } else {
    config.baseURL = Vue.prototype.myUrl
  }
  if (store.getters.token) {
    config.headers['x-auth-token'] = getToken() // 让每个请求携带token-- ['X-Token']为自定义key 请根据实际情况自行修改
  }
  config.headers['x-request-client'] = 'web'
   // 加密登录时，需要往请求头里加参数
   const data = config.data
   if (config.url === 'login' && data.prvKey) {
     config.headers['captcha-k'] = data.captchaKey
     config.headers['prv-k'] = data.prvKey
   }
  return config
}, error => {
  // Do something with request error
  console.log(error) // for debug
  Promise.reject(error)
})

// 去请求后拦截
service.interceptors.response.use(
  response => {
    // hideLoading()
    const res = response.data
    if (res.status === 0) {
      Message({
        message: res.message,
        type: 'error',
        duration: 5 * 1000
      })
      return response.data
    } else if (res.status === 2) {
      if (res.message) {
        Message({
          message: res.message,
          type: 'warning',
          duration: 5 * 1000
        })
        // 需要修改密码
        store.dispatch('changePwd', true)
      }
      return response.data
    // } else if (res.status === 1001) {
    //   MessageBox.confirm('你已被登出，可以取消继续留在该页面，或者重新登录', '确定登出', {
    //     confirmButtonText: '重新登录',
    //     cancelButtonText: '取消',
    //     type: 'warning'
    //   }).then(() => {
    //     store.dispatch('FedLogOut').then(() => {
    //       location.reload() // 为了重新实例化vue-router对象 避免bug
    //     })
    //   })
    //   return response.data
    } else {
      return response.data
    }
  },
  error => {
    // hideLoading()
    console.log('err' + error)// for debug
    Message({
      message: error.message,
      type: 'error',
      duration: 5 * 1000
    })
    return Promise.reject(error)
  })

export default service
