import request from '@/utils/request'

// export function channelAlertList(param) {
//   return request({
//     url: 'rest/channelAlert/list',
//     method: 'post',
//     data: param
//   })
// }

export function getOneChannel(param) {
  return request({
    url: 'rest/channel/get',
    method: 'post',
    data: param
  })
}

export function getChannalAlertInspect(param) {
  return request({
    url: 'rest/channelAlertInspect/list',
    method: 'post',
    data: param
  })
}

export function saveChannalAlertInspect(param) {
  return request({
    url: 'rest/channelAlertInspect/inspect',
    method: 'post',
    data: param
  })
}

export function getData(param) {
  return request({
    url: 'rest/data/getData',
    method: 'post',
    data: param
  })
}

// 获取多个设备的报警 deviceIds
export function getChannelAlertByDeviceIds(param) {
  return request({
    url: 'rest/channelAlertInspect/getChannelByDeviceIds',
    method: 'post',
    data: param
  })
}

// 预警事件以及关联设施设备信息
export function getChannleAlertListDiy(param) {
  return request({
    url: '/rest/channelAlertInspect/channelAlertDiyList',
    method: 'post',
    data: param
  })
}

// 获取数据
export function queryCarData(param) {
  return request({
    url: '/rest/data/queryCarData',
    method: 'post',
    data: param
  })
}

// 获取年车流量数据
export function monthandYearCarData(param) {
  return request({
    url: '/rest/carStatistics/monthandYearCarData',
    method: 'post',
    data: param
  })
}

// 获取车流量和维修单数据
export function biCarFlowDataAndOrderType(param) {
  return request({
    url: '/rest/carStatistics/biCarFlowDataAndOrderType',
    method: 'post',
    data: param
  })
}

// 日均同比环比车流量
export function dailYaverage(param) {
  return request({
    url: '/rest/carStatistics/dailYaverage',
    method: 'post',
    data: param
  })
}

// 年份交通流量
export function yearCarData(param) {
  return request({
    url: '/rest/carStatistics/yearCarData',
    method: 'post',
    data: param
  })
}

// 今天,昨天和去年的今天车流数据
export function dayYesterdayAndYearDay(param) {
  return request({
    url: '/rest/carStatistics/dayYesterdayAndYearDay',
    method: 'post',
    data: param
  })
}

// 车流量导入
export function importCarFlowData(param) {
  return request({
    url: '/rest/carStatistics/importCarFlowData',
    method: 'post',
    data: param
  })
}

// 车流量导入查询
export function queryCarFlowDataOk(param) {
  return request({
    url: '/rest/carStatistics/queryCarFlowDataOk',
    method: 'post',
    data: param
  })
}

// 车流量导入查询
export function updateCarFlowData(param) {
  return request({
    url: '/rest/carStatistics/updateCarFlowData',
    method: 'post',
    data: param
  })
}
// 大屏车流量接口
export function biCarStaData(param) {
  return request({
    url: '/rest/carStatistics/biCarStaData ',
    method: 'post',
    data: param
  })
}

