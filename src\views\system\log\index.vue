<!--系统日志-->
<template>
  <div class="app-container logDiv">
    <my-card title="系统日志">
      <div class="filter-container">
        <el-input
          class="filter-item"
          style="width: 200px"
          placeholder="用户"
          v-model="listQuery.params.name"
        >
        </el-input>
        <el-date-picker
          v-model="listQuery.params.myDate"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        >
        </el-date-picker>
        <el-button
          class="filter-item"
          type="primary"
          v-waves
          icon="el-icon-search"
          @click="search"
        >查询</el-button
        >
      </div>
      <table-list
        :data="list"
        :columns="columns"
        :list-loading="listLoading"
        class="dataTable"
        :total="total"
        :page-size="listQuery.pageSize"
        @currentChange="currentChange"
      ></table-list>
    </my-card>

    <el-dialog
      title="详情"
      :visible.sync="dialogFormVisible"
      custom-class="logDialog"
    >
      <el-row>
        <el-col :span="4" class="text-left"><label>用户:</label></el-col>
        <el-col :span="8">{{ detail.name }}</el-col>
        <el-col :span="4" class="text-left"><label>用户名:</label></el-col>
        <el-col :span="8">{{ detail.code }}</el-col>
      </el-row>
      <el-row>
        <el-col :span="4" class="text-left"><label>时间:</label></el-col>
        <el-col :span="8">{{
          detail.logDate | parseTime("{y}/{m}/{d} {h}:{i}:{s}")
        }}</el-col>
        <el-col :span="4" class="text-left"><label>IP地址:</label></el-col>
        <el-col :span="8">{{ detail.ip }}</el-col>
      </el-row>
      <el-row>
        <el-col :span="4" class="text-left"><label>访问路径:</label></el-col>
        <el-col :span="8">{{ detail.path }}</el-col>
        <el-col :span="4" class="text-left"><label>描述:</label></el-col>
        <el-col :span="8">{{ detail.remark }}</el-col>
      </el-row>
      <el-row>
        <el-col :span="4" class="text-left"><label>参数:</label></el-col>
        <el-col :span="20">{{ detail.param }}</el-col>
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import tableList from '@/components/table/tableList.vue'
import waves from '@/directive/waves'
import { getLogList } from '@/api/system/log'
import { parseTime } from '@/utils'
import MyCard from '@/components/MyCard'

export default {
  name: 'Log',
  components: {
    tableList, MyCard
  },
  directives: {
    waves
  },
  data() {
    return {
      dialogFormVisible: false, // 弹出框显示判断
      listLoading: false,
      columns: [
        {
          text: '用户',
          value: 'name'
        },
        {
          text: 'IP',
          value: 'ip'
        },
        {
          text: '访问路径',
          value: 'path'
        },
        {
          text: '描述',
          value: 'remark'
        },
        {
          text: '时间',
          value: 'logDate',
          filter: parseTime,
          filterParams: ['{y}/{m}/{d} {h}:{i}:{s}']
        },
        {
          text: '操作',
          type: 'iconButton',
          width: 80,
          list: this.operButton
        }
      ],
      listQuery: {
        pageSize: 15,
        currPage: 1,
        params: {
          name: null,
          myDate: null,
          startTime: null,
          endTime: null
        }
      },
      detail: {
        name: null,
        ip: null,
        code: null,
        path: null,
        remark: null,
        logDate: null,
        param: null
      },
      total: 0,
      list: []
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    // 获取所有角色
    getList() {
      this.listLoading = true
      getLogList(this.listQuery).then(response => {
        if (response.success) {
          this.list = response.result.list
          this.total = response.result.total
        }
        this.listLoading = false
      })
    },
    // 获取日志详情
    getLogDetail(val) {
      this.detail = val.row
      this.dialogFormVisible = true
    },
    search() {
      if (!this.listQuery.params.myDate) {
        this.listQuery.params.startTime = null
        this.listQuery.params.endTime = null
      } else {
        this.listQuery.params.startTime = parseTime(this.listQuery.params.myDate[0])
        this.listQuery.params.endTime = parseTime(this.listQuery.params.myDate[1])
      }
      this.getList()
    },
    // 表格操作按鈕
    operButton() {
      return [
        { class: 'icon-xiangqing1 ', value: '详情', click: this.getLogDetail }]
    },
    currentChange(val) {
      this.listQuery.currPage = val
      this.getList()
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss">
.logDialog {
  .el-row {
    padding: 10px;
  }
}
.logDiv {
  .filter-container {
    margin-left: 30px;
    display: flex;
    .filter-item,
    .el-date-editor {
      margin-left: 10px;
    }
  }
}
</style>
