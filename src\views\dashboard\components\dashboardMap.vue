<!-- description -->
<template>
  <div id="dashboard-map" style="width: 100%" class="dashboard-map"></div>
</template>

<script>
import { mapGetters, mapMutations } from 'vuex'
import { getNameByCode } from '@/utils'
import { fetchApiData } from '@/utils/messageBox'
import { getPicture } from '@/api/doc'
import { projectStatistics } from '@/api/bp'
import { getProjectList } from '../../../api/project/project'

export default {
  name: 'DashboardMap',
  components: {},
  directives: {},
  data() {
    return {
      map: null,
      infoWindow: null,
      projectList: [],
      resizeHandler: null
    }
  },
  computed: {
    ...mapGetters(['fileUrl', 'dictMap'])
  },
  watch: {
    dictMap(val) {
      if (!this.map) {
        this.setMap()
      }
    }
  },
  created() {
    if (this.dictMap && !this.map) {
      this.setMap()
    }
  },
  destroyed() {
    window.removeEventListener('resize', this.resizeHandler)
  },
  mounted() {
    this.handleResize()
    this.resizeHandler = window.addEventListener('resize', this.handleResize)
  },
  methods: {
    ...mapMutations(['SET_AVATAR', 'SET_IS_PROJECT']),
    setMap() {
      this.$nextTick(() => {
        setTimeout(() => {
          this.map = new AMap.Map('dashboard-map', {
            resizeEnable: true, // 是否监控地图容器尺寸变化
            center: [121.540239, 31.243893],
            zoom: 14,
            features: ['bg', 'road', 'building']
          })
          this.map.on('complete', this.markProjects)
          this.infoWindow = new AMap.InfoWindow({
            offset: new AMap.Pixel(0, -90)
          })

          const marker = new AMap.Marker({
            icon:
              'http://a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png',
            position: [121.540239, 31.243893],
            anchor: 'bottom-center'
          })
          marker.setMap(this.map)
        }, 300)
      })
    },
    goProject(item) {
      this.SET_IS_PROJECT(true)
      this.$storage.setStorage('project', { ...item })
      location.reload()
    },
    async markProjects() {
      this.projectList = await fetchApiData(
        getProjectList,
        {
          currPage: 1,
          pageSize: 2147483647,
          params: {}
        },
        'list'
      )
      for (const project of this.projectList) {
        if (
          project.location &&
          project.location.longitude &&
          project.location.latitude
        ) {
          await this.addMarker(project)
        }
      }
    },
    // 添加覆盖物
    async addMarker(project) {
      const image = require(`@/assets/dashboard/map/marker_type_${project.projectType ||
        1}.png`)
      const icon = new AMap.Icon({
        size: new AMap.Size(67.5, 90),
        image: image,
        imageSize: new AMap.Size(67.5, 90)
        // imageOffset: new AMap.Pixel(-14,-14)
      })
      const markerContent =
        '<div class="custom-content-marker"><img src="' + image + '"></div>'
      const marker = new AMap.Marker({
        icon: icon,
        content: markerContent,
        position: [project.location.longitude, project.location.latitude],
        cursor: 'pointer',
        offset: new AMap.Pixel(-30, -90),
        zoom: 13
      })
      marker.on('click', () => {
        this.goProject(project)
      })
      marker.content = await this.createInfoContent(project)
      marker.on('mouseover', this.markerMouseOver)
      marker.setMap(this.map)
    },
    async createInfoContent(project) {
      let src = ''
      if (project.docId) {
        const attach = await fetchApiData(
          getPicture,
          { docId: project.docId },
          '0'
        )
        src = `${this.fileUrl}/img/${attach.fileName}`
      }
      const statistic = await fetchApiData(
        projectStatistics,
        { id: project.id },
        '0'
      )
      if (!this.dictMap) {
        return ''
      }
      const structureType = getNameByCode(
        project.projectType,
        'structure_type'
      )
      return `<div class="info-content" style="width: 460px;height: 180px;padding: 10px">
        <img src='${src}' style="float: left;width: 240px;height: 160px;border-radius: 6px">
        <div class="title" style="float: right;width: 180px;line-height: 40px;color: rgba(96, 96, 98, 1)">
        <span style="font-size: 28px;font-weight: bold;">${
          project.detailAddress
        }</span><br>
        <span>结构型式：${structureType}</span><br>
        <span>测点总数：${
          statistic.device ? statistic.device.count : 0
        }</span><br>
        <span>测点报警：${
          statistic.alertUntreated ? statistic.alertUntreated.count : 0
        } 条</span>
        </div>
        </div>`
    },
    markerMouseOver(e) {
      this.infoWindow.setContent(e.target.content)
      this.infoWindow.open(this.map, e.target.getPosition())
    },
    handleResize() {
      // const dom = document.getElementsByClassName('app-main')[0]
      const appHeight = document.getElementById('app').clientHeight
      // document.getElementsByClassName('dashboard-map')[0].style.minHeight = '67.5rem'
      // document.getElementsByClassName('dashboard-map')[0].style.minWidth = '1920px'
      document.getElementsByClassName('dashboard-map')[0].style.height =
        appHeight - 49 + 'px'
      // document.getElementsByClassName('dashboard-map')[0].style.width = dom.clientWidth + 'px'
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss">
.dashboard-map {
  position: absolute;
  top: 0;
  background: white;
  .custom-content-marker {
    img {
      width: 68px;
      height: 98px;
    }

    .text {
      width: 30px;
      font-size: 12px;
      position: absolute;
      top: 18%;
      left: 40%;
    }
  }
}
#dashboard-map {
  .amap-info-content.amap-info-outer {
    border-radius: 8px;
  }
}
</style>
