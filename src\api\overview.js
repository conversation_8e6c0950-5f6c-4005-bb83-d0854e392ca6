import request from '@/utils/request'

// 监测类型列表
export function getMonitorTypes(params) {
    return request({
        url: '/rest/APicture/getMonitorTypes',
        method: 'post',
        data: params
    })
}
// 当日报警数量
export function getTodayWarningNum(params) {
    return request({
        url: '/rest/APicture/getTodayWarningNum',
        method: 'post',
        data: params
    })
}
// 查询报警事件
export function getWarningEvents(params) {
    return request({
        url: '/rest/APicture/getWarningEvents',
        method: 'post',
        data: params
    })
}
// 本周报警处置
export function getWeekWarningNum(params) {
    return request({
        url: '/rest/APicture/getWeekWarningNum',
        method: 'post',
        data: params
    })
}
// 边坡总数
export function getProTotal(params) {
    return request({
        url: '/rest/APicture/getProTotal',
        method: 'post',
        data: params
    })
}
// 边坡项目列表和地图项目
export function getGisProjects(params) {
    return request({
        url: '/rest/APicture/getGisProjects',
        method: 'post',
        data: params
    })
}
//  获取每个项目的预警状态
export function getProjectMaxWarningState(params) {
    return request({
        url: '/rest/APicture/getProjectMaxWarningState',
        method: 'post',
        data: params
    })
}
// 获取插值数据 温度,湿度,气温
export function getWeather(params) {
    return request({
        url: '/rest/APicture/getWeather',
        method: 'post',
        data: params
    })
}
// 获取边坡详情
export function getProjectExtend(params) {
    return request({
        url: '/rest/APicture/getProjectExtend',
        method: 'post',
        data: params
    })
}
