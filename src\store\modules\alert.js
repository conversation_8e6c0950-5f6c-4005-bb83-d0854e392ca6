const alert = {
  state: {
    newAlert: null // 是否有新的报警
  },
  mutations: {
    SET_NEW_ALERT: (state, alert) => {
      state.newAlert = alert
    },
    REMOVE_ALERT: (state) => {
      state.newAlert = null
    }
  },
  actions: {
    removeAlert({ commit }) {
      return new Promise((resolve) => {
        commit('REMOVE_ALERT')
      })
    },
    setNewAlert({ commit }, alert) {
      return new Promise((resolve) => {
        commit('SET_NEW_ALERT', alert)
      })
    }
  }
}

export default alert
