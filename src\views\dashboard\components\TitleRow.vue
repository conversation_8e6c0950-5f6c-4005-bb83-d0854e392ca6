<!-- description -->
<template>
  <div class="cb-title-row">
    <span class="title">{{ title }}</span>
    <slot></slot>
  </div>
</template>

<script>
export default {
  name: 'TitleRow',
  components: {},
  directives: {},
  props: {
    title: {
      type: String,
      default: ''
    }
  },
  data() {
    return {}
  },
  computed: {},
  created() {},
  mounted() {},
  methods: {}
}
</script>

<style rel="stylesheet/scss" lang="scss">
$basic-color: #1CADE4;
.cb-title-row {
  position: relative;
  margin-bottom: 10px;
  font-size: 16px;
  color: rgba(51, 97, 143, 1);
  .title {
    font-family: Source Han Sans CN;
    font-weight: bold;
    padding-left: 12px;
    margin-right: 15px;
    &::before {
      content: '';
      width: 4px;
      height: 16px;
      background: #1CADE4;
      position: absolute;
      left: 0;
      top: 1px;
    }
  }
  .title-btn {
    float: right;
    display: inline-block;
    border: 1px solid $basic-color;
    border-radius: 5px;
    color: $basic-color;
    font-size: 14px;
    padding: 5px 15px;
    cursor: pointer;
  }
}
</style>
