<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>IconFont Demo</title>
  <link rel="shortcut icon" href="https://gtms04.alicdn.com/tps/i4/TB1_oz6GVXXXXaFXpXXJDFnIXXX-64-64.ico" type="image/x-icon"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">&#xe86b;</a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>

      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=1128777" target="_blank" class="nav-more">查看项目</a>

    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">

            <li class="dib">
              <span class="icon iconfont">&#xe647;</span>
                <div class="name">新增</div>
                <div class="code-name">&amp;#xe647;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe664;</span>
                <div class="name">消息</div>
                <div class="code-name">&amp;#xe664;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe620;</span>
                <div class="name">组织架构</div>
                <div class="code-name">&amp;#xe620;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe603;</span>
                <div class="name">用户</div>
                <div class="code-name">&amp;#xe603;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe614;</span>
                <div class="name">密码</div>
                <div class="code-name">&amp;#xe614;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe75d;</span>
                <div class="name">字典</div>
                <div class="code-name">&amp;#xe75d;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe60e;</span>
                <div class="name">系统</div>
                <div class="code-name">&amp;#xe60e;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe611;</span>
                <div class="name">详情</div>
                <div class="code-name">&amp;#xe611;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe648;</span>
                <div class="name">角色管理</div>
                <div class="code-name">&amp;#xe648;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe625;</span>
                <div class="name">图片</div>
                <div class="code-name">&amp;#xe625;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe666;</span>
                <div class="name">数据</div>
                <div class="code-name">&amp;#xe666;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe602;</span>
                <div class="name">后台-权限管理</div>
                <div class="code-name">&amp;#xe602;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe605;</span>
                <div class="name">项目列表</div>
                <div class="code-name">&amp;#xe605;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe748;</span>
                <div class="name">待处理工单</div>
                <div class="code-name">&amp;#xe748;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe74e;</span>
                <div class="name">关联审计配置</div>
                <div class="code-name">&amp;#xe74e;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe672;</span>
                <div class="name">结构</div>
                <div class="code-name">&amp;#xe672;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe62c;</span>
                <div class="name">组织</div>
                <div class="code-name">&amp;#xe62c;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe61d;</span>
                <div class="name">设备类型管理</div>
                <div class="code-name">&amp;#xe61d;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xeac2;</span>
                <div class="name">二级-基础设施</div>
                <div class="code-name">&amp;#xeac2;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe70e;</span>
                <div class="name">下载</div>
                <div class="code-name">&amp;#xe70e;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe601;</span>
                <div class="name">日志</div>
                <div class="code-name">&amp;#xe601;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe608;</span>
                <div class="name">权限</div>
                <div class="code-name">&amp;#xe608;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xeb63;</span>
                <div class="name">关联设备</div>
                <div class="code-name">&amp;#xeb63;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe66f;</span>
                <div class="name">基础事件分类</div>
                <div class="code-name">&amp;#xe66f;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe6bb;</span>
                <div class="name">组织</div>
                <div class="code-name">&amp;#xe6bb;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe655;</span>
                <div class="name">主页</div>
                <div class="code-name">&amp;#xe655;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe600;</span>
                <div class="name">修改</div>
                <div class="code-name">&amp;#xe600;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe646;</span>
                <div class="name">用户角色管理</div>
                <div class="code-name">&amp;#xe646;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe6c7;</span>
                <div class="name">已处理</div>
                <div class="code-name">&amp;#xe6c7;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe604;</span>
                <div class="name">配置清单</div>
                <div class="code-name">&amp;#xe604;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe618;</span>
                <div class="name">删除</div>
                <div class="code-name">&amp;#xe618;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe606;</span>
                <div class="name">删 除</div>
                <div class="code-name">&amp;#xe606;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe697;</span>
                <div class="name">平台变量</div>
                <div class="code-name">&amp;#xe697;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe61e;</span>
                <div class="name">下载</div>
                <div class="code-name">&amp;#xe61e;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe662;</span>
                <div class="name">饼图统计</div>
                <div class="code-name">&amp;#xe662;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe62b;</span>
                <div class="name">查看</div>
                <div class="code-name">&amp;#xe62b;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe628;</span>
                <div class="name">预警</div>
                <div class="code-name">&amp;#xe628;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe690;</span>
                <div class="name">角色修改</div>
                <div class="code-name">&amp;#xe690;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe615;</span>
                <div class="name">资产</div>
                <div class="code-name">&amp;#xe615;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe645;</span>
                <div class="name">角色管理</div>
                <div class="code-name">&amp;#xe645;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe866;</span>
                <div class="name">ico_数据查询与统计_酒店定制报表</div>
                <div class="code-name">&amp;#xe866;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe6c9;</span>
                <div class="name">模型浏览</div>
                <div class="code-name">&amp;#xe6c9;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe739;</span>
                <div class="name">列表</div>
                <div class="code-name">&amp;#xe739;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe609;</span>
                <div class="name">统计</div>
                <div class="code-name">&amp;#xe609;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe6a3;</span>
                <div class="name">曲线图</div>
                <div class="code-name">&amp;#xe6a3;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe68a;</span>
                <div class="name">添加</div>
                <div class="code-name">&amp;#xe68a;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe629;</span>
                <div class="name">桥梁</div>
                <div class="code-name">&amp;#xe629;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe704;</span>
                <div class="name">项目管理</div>
                <div class="code-name">&amp;#xe704;</div>
              </li>

          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>兼容性最好，支持 IE6+，及所有现代浏览器。</li>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>但是因为是字体，所以不支持多色。只能使用平台里单色的图标，就算项目里有多色图标也会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持多色图标，这些多色图标在 Unicode 模式下将不能使用，如果有需求建议使用symbol 的引用方式</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: url('iconfont.eot');
  src: url('iconfont.eot?#iefix') format('embedded-opentype'),
      url('iconfont.woff2') format('woff2'),
      url('iconfont.woff') format('woff'),
      url('iconfont.ttf') format('truetype'),
      url('iconfont.svg#iconfont') format('svg');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">

          <li class="dib">
            <span class="icon iconfont iconxinzeng"></span>
            <div class="name">
              新增
            </div>
            <div class="code-name">.iconxinzeng
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont iconxiaoxi"></span>
            <div class="name">
              消息
            </div>
            <div class="code-name">.iconxiaoxi
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont iconzuzhijiagou"></span>
            <div class="name">
              组织架构
            </div>
            <div class="code-name">.iconzuzhijiagou
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont iconyonghu"></span>
            <div class="name">
              用户
            </div>
            <div class="code-name">.iconyonghu
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont iconmima"></span>
            <div class="name">
              密码
            </div>
            <div class="code-name">.iconmima
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont iconzidian"></span>
            <div class="name">
              字典
            </div>
            <div class="code-name">.iconzidian
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont iconxitong"></span>
            <div class="name">
              系统
            </div>
            <div class="code-name">.iconxitong
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont iconxiangqing"></span>
            <div class="name">
              详情
            </div>
            <div class="code-name">.iconxiangqing
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont iconjiaoseguanli"></span>
            <div class="name">
              角色管理
            </div>
            <div class="code-name">.iconjiaoseguanli
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icontupian"></span>
            <div class="name">
              图片
            </div>
            <div class="code-name">.icontupian
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont iconshuju"></span>
            <div class="name">
              数据
            </div>
            <div class="code-name">.iconshuju
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont iconquanxianguanli"></span>
            <div class="name">
              后台-权限管理
            </div>
            <div class="code-name">.iconquanxianguanli
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont iconccgl-xiangmuguanli-3"></span>
            <div class="name">
              项目列表
            </div>
            <div class="code-name">.iconccgl-xiangmuguanli-3
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont iconorder-pending"></span>
            <div class="name">
              待处理工单
            </div>
            <div class="code-name">.iconorder-pending
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont iconconfig-link-audit"></span>
            <div class="name">
              关联审计配置
            </div>
            <div class="code-name">.iconconfig-link-audit
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont iconjiegou"></span>
            <div class="name">
              结构
            </div>
            <div class="code-name">.iconjiegou
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont iconfl-zuzhi"></span>
            <div class="name">
              组织
            </div>
            <div class="code-name">.iconfl-zuzhi
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont iconshebeileixingguanli"></span>
            <div class="name">
              设备类型管理
            </div>
            <div class="code-name">.iconshebeileixingguanli
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont iconerji-jichusheshi"></span>
            <div class="name">
              二级-基础设施
            </div>
            <div class="code-name">.iconerji-jichusheshi
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont iconicon--"></span>
            <div class="name">
              下载
            </div>
            <div class="code-name">.iconicon--
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont iconrizhi"></span>
            <div class="name">
              日志
            </div>
            <div class="code-name">.iconrizhi
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont iconpermisssion-management"></span>
            <div class="name">
              权限
            </div>
            <div class="code-name">.iconpermisssion-management
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont iconguanlianshebei"></span>
            <div class="name">
              关联设备
            </div>
            <div class="code-name">.iconguanlianshebei
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont iconjichushijianfenlei"></span>
            <div class="name">
              基础事件分类
            </div>
            <div class="code-name">.iconjichushijianfenlei
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont iconzuzhi"></span>
            <div class="name">
              组织
            </div>
            <div class="code-name">.iconzuzhi
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont iconzhuye"></span>
            <div class="name">
              主页
            </div>
            <div class="code-name">.iconzhuye
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont iconxiugai"></span>
            <div class="name">
              修改
            </div>
            <div class="code-name">.iconxiugai
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont iconyonghujiaoseguanli"></span>
            <div class="name">
              用户角色管理
            </div>
            <div class="code-name">.iconyonghujiaoseguanli
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont iconyichuli"></span>
            <div class="name">
              已处理
            </div>
            <div class="code-name">.iconyichuli
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont iconic_configchecklist"></span>
            <div class="name">
              配置清单
            </div>
            <div class="code-name">.iconic_configchecklist
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont iconshanchu"></span>
            <div class="name">
              删除
            </div>
            <div class="code-name">.iconshanchu
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont iconshanchu1"></span>
            <div class="name">
              删 除
            </div>
            <div class="code-name">.iconshanchu1
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont iconpingtaibianliang"></span>
            <div class="name">
              平台变量
            </div>
            <div class="code-name">.iconpingtaibianliang
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont iconxiazai"></span>
            <div class="name">
              下载
            </div>
            <div class="code-name">.iconxiazai
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont iconbingtutongji"></span>
            <div class="name">
              饼图统计
            </div>
            <div class="code-name">.iconbingtutongji
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont iconchakan-copy"></span>
            <div class="name">
              查看
            </div>
            <div class="code-name">.iconchakan-copy
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont iconyujing"></span>
            <div class="name">
              预警
            </div>
            <div class="code-name">.iconyujing
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont iconjiaosexiugai"></span>
            <div class="name">
              角色修改
            </div>
            <div class="code-name">.iconjiaosexiugai
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont iconziyuan"></span>
            <div class="name">
              资产
            </div>
            <div class="code-name">.iconziyuan
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont iconjiaoseguanli1"></span>
            <div class="name">
              角色管理
            </div>
            <div class="code-name">.iconjiaoseguanli1
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont iconico_shujuchaxunyutongji_jiudiandingzhibaobiao"></span>
            <div class="name">
              ico_数据查询与统计_酒店定制报表
            </div>
            <div class="code-name">.iconico_shujuchaxunyutongji_jiudiandingzhibaobiao
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont iconmoxingliulan"></span>
            <div class="name">
              模型浏览
            </div>
            <div class="code-name">.iconmoxingliulan
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont iconliebiao"></span>
            <div class="name">
              列表
            </div>
            <div class="code-name">.iconliebiao
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icontongji1"></span>
            <div class="name">
              统计
            </div>
            <div class="code-name">.icontongji1
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont iconquxiantu"></span>
            <div class="name">
              曲线图
            </div>
            <div class="code-name">.iconquxiantu
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icontianjia1"></span>
            <div class="name">
              添加
            </div>
            <div class="code-name">.icontianjia1
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont iconqiaoliang"></span>
            <div class="name">
              桥梁
            </div>
            <div class="code-name">.iconqiaoliang
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont iconxiangmuguanli"></span>
            <div class="name">
              项目管理
            </div>
            <div class="code-name">.iconxiangmuguanli
            </div>
          </li>

        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>兼容性良好，支持 IE8+，及所有现代浏览器。</li>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
          <li>不过因为本质上还是使用的字体，所以多色图标还是不支持的。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont iconxxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconxinzeng"></use>
                </svg>
                <div class="name">新增</div>
                <div class="code-name">#iconxinzeng</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconxiaoxi"></use>
                </svg>
                <div class="name">消息</div>
                <div class="code-name">#iconxiaoxi</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconzuzhijiagou"></use>
                </svg>
                <div class="name">组织架构</div>
                <div class="code-name">#iconzuzhijiagou</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconyonghu"></use>
                </svg>
                <div class="name">用户</div>
                <div class="code-name">#iconyonghu</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconmima"></use>
                </svg>
                <div class="name">密码</div>
                <div class="code-name">#iconmima</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconzidian"></use>
                </svg>
                <div class="name">字典</div>
                <div class="code-name">#iconzidian</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconxitong"></use>
                </svg>
                <div class="name">系统</div>
                <div class="code-name">#iconxitong</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconxiangqing"></use>
                </svg>
                <div class="name">详情</div>
                <div class="code-name">#iconxiangqing</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconjiaoseguanli"></use>
                </svg>
                <div class="name">角色管理</div>
                <div class="code-name">#iconjiaoseguanli</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icontupian"></use>
                </svg>
                <div class="name">图片</div>
                <div class="code-name">#icontupian</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconshuju"></use>
                </svg>
                <div class="name">数据</div>
                <div class="code-name">#iconshuju</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconquanxianguanli"></use>
                </svg>
                <div class="name">后台-权限管理</div>
                <div class="code-name">#iconquanxianguanli</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconccgl-xiangmuguanli-3"></use>
                </svg>
                <div class="name">项目列表</div>
                <div class="code-name">#iconccgl-xiangmuguanli-3</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconorder-pending"></use>
                </svg>
                <div class="name">待处理工单</div>
                <div class="code-name">#iconorder-pending</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconconfig-link-audit"></use>
                </svg>
                <div class="name">关联审计配置</div>
                <div class="code-name">#iconconfig-link-audit</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconjiegou"></use>
                </svg>
                <div class="name">结构</div>
                <div class="code-name">#iconjiegou</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconfl-zuzhi"></use>
                </svg>
                <div class="name">组织</div>
                <div class="code-name">#iconfl-zuzhi</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconshebeileixingguanli"></use>
                </svg>
                <div class="name">设备类型管理</div>
                <div class="code-name">#iconshebeileixingguanli</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconerji-jichusheshi"></use>
                </svg>
                <div class="name">二级-基础设施</div>
                <div class="code-name">#iconerji-jichusheshi</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconicon--"></use>
                </svg>
                <div class="name">下载</div>
                <div class="code-name">#iconicon--</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconrizhi"></use>
                </svg>
                <div class="name">日志</div>
                <div class="code-name">#iconrizhi</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconpermisssion-management"></use>
                </svg>
                <div class="name">权限</div>
                <div class="code-name">#iconpermisssion-management</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconguanlianshebei"></use>
                </svg>
                <div class="name">关联设备</div>
                <div class="code-name">#iconguanlianshebei</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconjichushijianfenlei"></use>
                </svg>
                <div class="name">基础事件分类</div>
                <div class="code-name">#iconjichushijianfenlei</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconzuzhi"></use>
                </svg>
                <div class="name">组织</div>
                <div class="code-name">#iconzuzhi</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconzhuye"></use>
                </svg>
                <div class="name">主页</div>
                <div class="code-name">#iconzhuye</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconxiugai"></use>
                </svg>
                <div class="name">修改</div>
                <div class="code-name">#iconxiugai</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconyonghujiaoseguanli"></use>
                </svg>
                <div class="name">用户角色管理</div>
                <div class="code-name">#iconyonghujiaoseguanli</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconyichuli"></use>
                </svg>
                <div class="name">已处理</div>
                <div class="code-name">#iconyichuli</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconic_configchecklist"></use>
                </svg>
                <div class="name">配置清单</div>
                <div class="code-name">#iconic_configchecklist</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconshanchu"></use>
                </svg>
                <div class="name">删除</div>
                <div class="code-name">#iconshanchu</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconshanchu1"></use>
                </svg>
                <div class="name">删 除</div>
                <div class="code-name">#iconshanchu1</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconpingtaibianliang"></use>
                </svg>
                <div class="name">平台变量</div>
                <div class="code-name">#iconpingtaibianliang</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconxiazai"></use>
                </svg>
                <div class="name">下载</div>
                <div class="code-name">#iconxiazai</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconbingtutongji"></use>
                </svg>
                <div class="name">饼图统计</div>
                <div class="code-name">#iconbingtutongji</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconchakan-copy"></use>
                </svg>
                <div class="name">查看</div>
                <div class="code-name">#iconchakan-copy</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconyujing"></use>
                </svg>
                <div class="name">预警</div>
                <div class="code-name">#iconyujing</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconjiaosexiugai"></use>
                </svg>
                <div class="name">角色修改</div>
                <div class="code-name">#iconjiaosexiugai</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconziyuan"></use>
                </svg>
                <div class="name">资产</div>
                <div class="code-name">#iconziyuan</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconjiaoseguanli1"></use>
                </svg>
                <div class="name">角色管理</div>
                <div class="code-name">#iconjiaoseguanli1</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconico_shujuchaxunyutongji_jiudiandingzhibaobiao"></use>
                </svg>
                <div class="name">ico_数据查询与统计_酒店定制报表</div>
                <div class="code-name">#iconico_shujuchaxunyutongji_jiudiandingzhibaobiao</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconmoxingliulan"></use>
                </svg>
                <div class="name">模型浏览</div>
                <div class="code-name">#iconmoxingliulan</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconliebiao"></use>
                </svg>
                <div class="name">列表</div>
                <div class="code-name">#iconliebiao</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icontongji1"></use>
                </svg>
                <div class="name">统计</div>
                <div class="code-name">#icontongji1</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconquxiantu"></use>
                </svg>
                <div class="name">曲线图</div>
                <div class="code-name">#iconquxiantu</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icontianjia1"></use>
                </svg>
                <div class="name">添加</div>
                <div class="code-name">#icontianjia1</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconqiaoliang"></use>
                </svg>
                <div class="name">桥梁</div>
                <div class="code-name">#iconqiaoliang</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconxiangmuguanli"></use>
                </svg>
                <div class="name">项目管理</div>
                <div class="code-name">#iconxiangmuguanli</div>
            </li>

          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
