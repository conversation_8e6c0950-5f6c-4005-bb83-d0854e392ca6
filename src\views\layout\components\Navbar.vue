<template>
  <el-menu class="navbar" mode="horizontal">
    <hamburger class="hamburger-container" :toggle-click="toggleSideBar" :is-active="sidebar.opened"></hamburger>

    <!--<breadcrumb class="breadcrumb-container"></breadcrumb>-->
    <span style="color: #fff">{{ projectName }}</span>
    <!--<el-button button-privilege="p_fhzy" style="background: #5d6dc3;border: 1px solid #5d6dc3;">-->
    <i class="icon iconfont iconzuzhi" @click="goSystem" style="color: #fff;margin-left: 10px;cursor: pointer"
       v-if="isProject"></i>
    <!--</el-button>-->
    <div class="right-menu">
      <!--      <error-log class="errLog-container right-menu-item"></error-log>-->

      <el-tooltip effect="dark" content="全屏" placement="bottom">
        <screenfull class="screenfull right-menu-item"></screenfull>
      </el-tooltip>

      <!--      <div class="messageDiv">-->
      <!--        <i class="icon iconfont iconxiaoxi" @click="goMessage"></i>-->
      <!--        <span>{{ messageNum }}</span>-->
      <!--        &lt;!&ndash;<el-badge :value="12" class="item">&ndash;&gt;-->
      <!--        &lt;!&ndash;<el-button size="small">评论</el-button>&ndash;&gt;-->
      <!--        &lt;!&ndash;</el-badge>&ndash;&gt;-->
      <!--      </div>-->

      <!--      <el-tooltip effect="dark" content="换肤" placement="bottom">-->
      <!--        <theme-picker class="theme-switch right-menu-item"></theme-picker>-->
      <!--      </el-tooltip>-->

      <el-dropdown class="avatar-container right-menu-item" trigger="click">
        <div class="avatar-wrapper">
          <img class="user-avatar" :src="fileUrl+'img/'+avatar+'?size=80x80'"
               v-if="avatar&&avatar != ''&&fileUrl != ''">
          <img class="user-avatar" :src="defaultHead" v-else>
          <i class="el-icon-caret-bottom" style="color: #fff"></i>
        </div>
        <el-dropdown-menu slot="dropdown">
          <!--<router-link to="/">-->
          <!--<el-dropdown-item>-->
          <!--首页-->
          <!--</el-dropdown-item>-->
          <!--</router-link>-->
          <el-dropdown-item>
            <span @click="imagecropperShow = true" style="display:block;">头像</span>
          </el-dropdown-item>
          <el-dropdown-item divided>
            <span @click="passwordFormVisible = true" style="display:block;">修改密码</span>
          </el-dropdown-item>
          <el-dropdown-item divided>
            <span @click="logout" style="display:block;">登出</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <image-cropper :width="300" :height="300" url="/rest/user/uploadPhoto" @close="close"
                     @crop-upload-success="cropSuccess" field="attach"
                     :key="imagecropperKey" v-show="imagecropperShow"></image-cropper>

      <el-dialog title="修改密码" :visible.sync="passwordFormVisible">
        <el-form :rules="passwordRules" ref="passwordForm" :model="passwordForm" label-position="center" size="small"
                 label-width="100px">
          <el-row>
            <el-col :span="12">
              <el-form-item label="旧密码" prop="password">
                <el-input type="password" class="filter-item" placeholder="请输入旧密码" v-model="passwordForm.password">
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="新密码" prop="newPassword">
                <el-input type="password" class="filter-item" placeholder="请输入新密码" v-model="passwordForm.newPassword">
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="确认密码" prop="newPasswordPre">
                <el-input type="password" class="filter-item" placeholder="请输入确认密码"
                          v-model="passwordForm.newPasswordPre">
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="passwordFormVisible = false">取消</el-button>
          <el-button type="primary" @click="passwordResetSubimt" :loading="loading" v-waves>提交</el-button>
        </div>
      </el-dialog>

    </div>
  </el-menu>
</template>

<script>
  import { mapGetters, mapMutations } from 'vuex'
  import Breadcrumb from '@/components/Breadcrumb'
  import Hamburger from '@/components/Hamburger'
  import ErrorLog from '@/components/ErrorLog'
  import Screenfull from '@/components/Screenfull'
  import ThemePicker from '@/components/ThemePicker'
  import { removeToken } from '@/utils/auth'
  import ImageCropper from '@/components/ImageCropper'
  import defaultHead from '@/assets/dashboard/defaultHead.png'
  import waves from '@/directive/waves'
  import { updatePassword } from '@/api/system/person'

  export default {
    components: {
      Breadcrumb,
      Hamburger,
      ErrorLog,
      Screenfull,
      ThemePicker,
      ImageCropper
    },
    directives: {
      waves
    },
    data() {
      const validatePassword = (rule, value, callback) => {
        if (value !== this.passwordForm.newPassword) {
          callback(new Error('两次输入密码不一致'))
        } else if (value === '') {
          callback(new Error('请输入确认密码'))
        } else {
          callback()
        }
      }
      return {
        projectName: '浦东大道地道全生命周期智慧运维',
        defaultHead,
        imagecropperShow: false,
        imagecropperKey: 0,
        passwordFormVisible: false,
        passwordForm: {
          password: '',
          newPassword: '',
          newPasswordPre: ''
        },
        loading: false,
        passwordRules: { // 属性校验
          password: [
            { required: true, message: '请输入旧密码', trigger: 'blur' }
          ],
          newPassword: [
            { required: true, message: '请输入新密码', trigger: 'blur' }
          ],
          newPasswordPre: [
            { required: true, validator: validatePassword, trigger: 'blur' }
          ]
        }
        // privilege: 'p_fhzy'
      }
    },
    computed: {
      ...mapGetters([
        'sidebar',
        'name',
        'avatar',
        'fileUrl',
        'messageNum',
        'isProject',
        'projectButtonPrivileges'
      ]),
      changePwdFlag() {
        return this.$store.state.user.changePwdFlag
      }
    },
     watch: {
      changePwdFlag: {
        immediate: true,
        handler(val) {
          this.passwordFormVisible = val
        }
      }
    },
    mounted() {
      if (this.$storage.getStorage('project')) {
        this.projectName = JSON.parse(this.$storage.getStorage('project')).name
      }
    },
    methods: {
      ...mapMutations([
        'SET_AVATAR', 'SET_IS_PROJECT'
      ]),
      passwordResetSubimt() {
        this.$refs['passwordForm'].validate((valid) => {
          if (valid) {
            this.loading = true
            updatePassword({
              originPassword: this.passwordForm.password,
              currentPassword: this.passwordForm.newPassword
            }).then((response) => {
              if (response.success) {
                this.$message.success(response.message)
                this.$refs['passwordForm'].resetFields()
                this.passwordFormVisible = false
              }
              this.loading = false
            })
          }
        })
      },
      toggleSideBar() {
        this.$store.dispatch('toggleSideBar')
      },
      logout() {
        this.$store.dispatch('LogOut').then(() => {
          removeToken()
          location.reload()// In order to re-instantiate the vue-router object to avoid bugs
        })
      },
      cropSuccess(resData) {
        this.imagecropperShow = false
        this.imagecropperKey = this.imagecropperKey + 1
        this.SET_AVATAR(resData.result)
      },
      close() {
        this.imagecropperShow = false
      },
      goMessage() {
        this.$router.push({ path: '/system/message' })
      },
      goSystem() {
        console.log(document.getElementById('fhzy'))
        if (this.isProject) {
          this.SET_IS_PROJECT(false)
          this.$storage.removeStorage('project')
          // this.$router.push({ path: '/dashboard' })
          location.reload()
        }
        // else {
        //   this.SET_IS_PROJECT(false)
        //   this.$router.push({ path: '/projectDash/projectDashboard' })
        //   location.reload()
        // }
      }
    }
  }
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
  .navbar {
    height: 50px;
    line-height: 50px;
    width: 100% !important;
    background-color: rgba(93, 109, 195, 1);
    border-radius: 0px !important;
    .hamburger-container {
      line-height: 58px;
      height: 50px;
      float: left;
      padding: 0 10px;
    }
    .messageDiv {
      display: inline-block;
      width: 30px;
      .icon {
        font-size: 24px;
        display: inline-block;
        cursor: pointer;
        color: #fff;
        vertical-align: 10px;
      }
      span {
        position: absolute;
        margin-top: 4px;
        margin-left: -9px;
        color: #fff;
        background: red;
        height: 20px;
        padding: 5px;
        font-size: 12px;
        line-height: 12px;
        border-radius: 50%;
      }
    }
    .breadcrumb-container {
      float: left;
    }
    .errLog-container {
      display: inline-block;
      vertical-align: top;
    }
    .right-menu {
      float: right;
      height: 100%;
      &:focus {
        outline: none;
      }
      .right-menu-item {
        display: inline-block;
        margin: 0 8px;
      }
      .screenfull {
        height: 20px;
      }
      .international {
        vertical-align: top;
      }
      .theme-switch {
        vertical-align: 15px;
      }
      .avatar-container {
        height: 50px;
        margin-right: 30px;
        .avatar-wrapper {
          cursor: pointer;
          margin-top: 5px;
          position: relative;
          .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 10px;
          }
          .el-icon-caret-bottom {
            position: absolute;
            right: -20px;
            top: 25px;
            font-size: 12px;
          }
        }
      }
    }
  }
</style>
