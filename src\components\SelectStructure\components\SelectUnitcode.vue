<template>
  <el-autocomplete
    class="filter-item"
    v-model="code"
    placeholder="请输入编码"
    :fetch-suggestions="queryCodeAsync"
    @select="handleCodeSelect"
    select-when-unmatched>
  </el-autocomplete>
</template>

<script>
  // import { getUnitCodeList } from '@/api/project/unitCode'

  export default {
    name: 'SelectUnitcode',
    model: {
      prop: 'unitcode',
      event: 'change'
    },
    props: {
      unitcode: {
        type: Object,
        default: () => {}
      },
      type: {
        type: String,
        default: ''
      }
    },
    data() {
      return {
        code: this.unitcode.code,
        typeOptions: [],
        structureTypeList: [],
        typeLetterMap: {}
      }
    },
    watch: {
      code(val) {
        if (val === '') {
          this.$emit('change', {
            code: '',
            position: '',
            mileageValue: '',
            rangeQuery: ''
          })
        }
      },
      unitcode() {
        this.code = this.unitcode.code
      }
    },
    mounted() {
    },
    methods: {
      // 摄像机位置 & 里程桩号编号关联
      queryCodeAsync(queryString, cb) {
        // getUnitCodeList({ code: this.code, mileage: this.code, type: this.type }).then(response => {
        //   if (response.success) {
        //     response.result.list.forEach(item => {
        //       item.value = item.code
        //       return false
        //     })
        //     cb(response.result.list)
        //   }
        // })
      },
      handleCodeSelect(item) {
        this.code = item.code
        this.$emit('change', {
          code: item.code,
          position: item.position,
          mileageValue: item.mileageValue,
          rangeQuery: item.rangeQuery
        })
      }
    }
  }
</script>

<style scoped>
</style>
