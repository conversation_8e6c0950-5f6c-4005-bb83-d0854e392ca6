// import { JSEncrypt } from 'encryptlong' // 需要引入encryptlong【已引入】
import { JSEncrypt } from 'encryptlong/bin/jsencrypt.min.js' // 需要引入encryptlong【已引入】
// import JSEncrypt from 'jsencrypt' // 需要引入jsencrypt【未引入】

// 私钥
const privateKey = ''

// 公钥加密
export function encrypt(publicKey, passwd) {
  const encryptor = new JSEncrypt()
  encryptor.setPublicKey(publicKey)
  // return encryptor.encrypt(passwd)
  return encryptor.encryptLong(passwd)
}

// 私钥解密
export function decrypt(passwd) {
  const encryptor = new JSEncrypt()
  encryptor.setPrivateKey(privateKey)
  // return encryptor.decrypt(passwd)
  return encryptor.encryptLong(passwd)
}
