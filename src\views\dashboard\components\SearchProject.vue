<!-- description -->
<template>
  <div class="sysDashboardSearch">
    <div class="search-btn" @click.stop="handleClick">
      <div>
        <icon-search />
      </div>
    </div>
    <pannel-basic
      :visible.sync="showPannel"
      title="项目查询"
      class="project-pannel"
    >
      <el-input v-model="postParam.params.name"></el-input>
      <el-row style="display: flex; justify-content: center; margin: 10px 0">
        <el-button type="info">取消</el-button>
        <el-button type="primary" @click="search">确认</el-button>
      </el-row>
      <list-basic
        style="max-height: 108px"
        :list="projectList"
        @click="projectClick"
      >
      </list-basic>
    </pannel-basic>
  </div>
</template>

<script>
import PannelBasic from '@/components/Dashboard/PannelBasic'
import IconSearch from '@/components/Dashboard/icon/IconSearch'
import { fetchApiData } from '@/utils/messageBox'
import ListBasic from '@/components/Dashboard/ListBasic'
// import { bpProjectList } from '@/api/bp'
import { getProjectList } from '@/api/project/project'

export default {
  name: 'SearchProject',
  components: { ListBasic, IconSearch, PannelBasic },
  directives: {},
  data() {
    return {
      showPannel: false,
      postParam: {
        currPage: 1,
        pageSize: 2147483647,
        params: { name: '' }
      },
      projectList: []
    }
  },
  computed: {},
  created() { },
  mounted() { },
  methods: {
    handleClick() {
      this.showPannel = true
    },
    async search() {
      this.projectList = await fetchApiData(getProjectList, this.postParam, 'list')
    },
    projectClick(item) {
      this.$emit('projectClick', item)
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss">
@import "src/styles/dashboard.scss";
.sysDashboardSearch {
  position: absolute;
  right: 20px;
  top: 20px;
  width: 510px;
  .search-btn {
    cursor: pointer;
    position: absolute;
    top: 0;
    right: 0;
    width: 60px;
    height: 60px;
    background: white;
    box-shadow: 2px 4px 8px rgba(0, 0, 0, 0.35);
    border-radius: 4.28571px;
    > div {
      position: absolute;
      left: 12.5%;
      right: 14.62%;
      top: 12.5%;
      bottom: 14.63%;
    }
  }
  .project-pannel {
    position: absolute;
    top: 0;
    left: 0;
    width: 440px;
    height: 312px;
  }
}
</style>
