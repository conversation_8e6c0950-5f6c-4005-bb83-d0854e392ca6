import request from '@/utils/request'

// 病害类型列表
export function getDefectTypeList(param) {
  return request({
    url: 'rest/defectType/list',
    method: 'post',
    data: param
  })
}

// 病害保存
export function saveDefectType(param) {
  return request({
    url: 'rest/defectType/save',
    method: 'post',
    data: param
  })
}
// 获取病害类型记录
export function getDefectType(param) {
  return request({
    url: 'rest/defectType/get',
    method: 'post',
    data: param
  })
}

export function deleteDefectType(param) {
  return request({
    url: 'rest/defectType/delete',
    method: 'post',
    data: param
  })
}

