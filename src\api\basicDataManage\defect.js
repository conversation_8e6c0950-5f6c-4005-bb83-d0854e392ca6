import request from '@/utils/request'

export function saveDefect(param) {
  return request({
    url: 'rest/defect/save',
    method: 'post',
    data: param
  })
}

export function defectList(param) {
  return request({
    url: 'rest/defect/list',
    method: 'post',
    data: param
  })
}

export function getDefectInfo(param) {
  return request({
    url: 'rest/defect/get',
    method: 'post',
    data: param
  })
}

export function deleteDefect(param) {
  return request({
    url: 'rest/defect/delete',
    method: 'post',
    data: param
  })
}

// 根据设施或设备查询已有缺陷
export function existDefectList(param) {
  return request({
    url: 'rest/defect/existList',
    method: 'post',
    data: param
  })
}

export function confirmDefect(param) {
  return request({
    url: 'rest/defect/confirm',
    method: 'post',
    data: param
  })
}

// 二级确认缺陷
export function secondConfirmDefect(param) {
  return request({
    url: 'rest/defect/secondConfirm',
    method: 'post',
    data: param
  })
}

export function createOrder(param) {
  return request({
    url: 'rest/defect/createOrder',
    method: 'post',
    data: param
  })
}

export function getByOrderId(param) {
  return request({
    url: 'rest/defect/getByOrderId',
    method: 'post',
    data: param
  })
}

export function hisDefectListByStructureId(param) {
  return request({
    url: 'rest/historicalDefect/hisDefectListByStructureId',
    method: 'post',
    data: param
  })
}

export function defectListByStructureId(param) {
  return request({
    url: 'rest/defect/defectListByStructureId',
    method: 'post',
    data: param
  })
}
// 获取上一页和下一页id
export function getLastandNextId(param) {
  return request({
    url: '/rest/defect/queryIdsByCurrentId',
    method: 'post',
    data: param
  })
}

export function importDefectList(param) {
  return request({
    url: '/rest/defect/importDefectList',
    method: 'post',
    data: param
  })
}

// 计算缺陷严重程度
export function getDefectSeverity(param) {
  return request({
    url: '/rest/defect/getDefectSeverity',
    method: 'post',
    data: param
  })
}

// 查询大类小类
export function getBigLittleInfo(param) {
  return request({
    url: '/rest/defect/getBigLittleInfo',
    method: 'post',
    data: param
  })
}
