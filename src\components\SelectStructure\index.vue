<!-- 选择设施或设备 -->
<template>
  <el-row>
    <el-input
      v-model="dataName"
      @click.native="selectStructure"
      type="textarea"
      autosize
    >
    </el-input>
    <el-dialog
      :title="'选择' + titleMap[type]"
      :visible.sync="visible"
      @close="cancel"
      :close-on-click-modal="false"
      width="60%"
      append-to-body
      class="selectStructure"
    >
      <div class="filter-container">
        <template v-if="isDeviceGroup">
          <el-select
            class="filter-item"
            v-model="listQuery.params.deviceGroupType"
            style="width: 180px"
            placeholder="请选择设备组类型"
          >
            <el-option
              v-for="item in dictMap['device_group_type']"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            >
            </el-option>
          </el-select>
        </template>
        <template v-if="isStructure">
          <structure-type
            v-model="listQuery.params.type"
            style="width: 300px"
          ></structure-type>
        </template>
        <template v-if="isDevice">
          <!--          <structure-tree v-model="structureId"></structure-tree>-->
          <el-select
            class="filter-item"
            v-model="listQuery.params.deviceParentTypeId"
            style="width: 180px"
            placeholder="请选择设备大类"
            clearable
            @change="changeSearchParentDeviceType"
          >
            <el-option
              v-for="item in deviceParentTypeList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
          <el-select
            class="filter-item"
            style="width: 180px"
            v-model="listQuery.params.deviceTypeId"
            clearable
            placeholder="请选择设备小类"
            no-data-text="请先选择设备大类"
            @change="changeSearchDeviceType"
          >
            <el-option
              v-for="item in deviceSearchTypeList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
          <el-input
            v-model="listQuery.params.name"
            placeholder="请输入关键字"
            class="filter-item"
            style="width: 150px"
          ></el-input>
        </template>
        <el-button
          class="filter-item"
          type="primary"
          v-waves
          icon="el-icon-search"
          @click="search"
        >查询</el-button
        >
        <el-button
          class="filter-item"
          type="info"
          icon="el-icon-refresh"
          @click="searchReset"
        >重置</el-button
        >
      </div>
      <el-row>
        <el-col :span="11">
          <el-card>
            <div slot="header" class="clearfix">
              <span>{{ titleMap[type] }}列表</span>
            </div>
            <table-list
              :columns="columns"
              class="dataTable"
              :page-size="listQuery.pageSize"
              :page-num="listQuery.currPage"
              :page-count="4"
              page-small
              v-if="showList"
              :total="total"
              :data="list"
              @currentChange="currentChange"
              style="width: 100%"
            ></table-list>
          </el-card>
        </el-col>
        <el-col :span="2">
          <div class="arrowDiv">
            <div><i class="el-icon-back"></i></div>
            <div><i class="el-icon-back"></i></div>
          </div>
        </el-col>
        <el-col :span="11">
          <el-card>
            <div slot="header" class="clearfix">
              <span>已选{{ titleMap[type] }}</span>
            </div>
            <table-list
              :columns="selectedColumns"
              v-if="showCurrList"
              class="dataTable"
              :page-size="10"
              :page-num="currPage"
              page-small
              @currentChange="selectedChange"
              :data="currList"
              :total="selectedTotal"
            ></table-list>
          </el-card>
        </el-col>
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" @click="confirm">确定</el-button>
      </div>
    </el-dialog>
  </el-row>
</template>

<script>
import waves from '@/directive/waves'
import StructureType from '@/components/SelectStructure/components/StructureType'
import SelectUnitcode from '@/components/SelectStructure/components/SelectUnitcode'
import DeviceCategory from '@/components/SelectStructure/components/DeviceCategory'
import StructureTree from '@/components/SelectStructure/components/StructureTree'
import TableList from '@/components/table/tableList.vue'
// import { getStructureListByUnitCode } from '@/api/project/unitCode'
// import { getDeviceListByUnitCode } from '@/api/project/unitCode'
import { getDeviceList } from '@/api/project/device'
import { getDeviceGroupList } from '@/api/project/deviceGroup'
import { getDeviceTypeList, getSonListByParentId } from '@/api/system/device'
import { mapGetters } from 'vuex'
import { structureListPage } from '@/api/project/structure'

export default {
  name: 'SelectStructure',
  components: { TableList, StructureType, SelectUnitcode, StructureTree, DeviceCategory },
  directives: { waves },
  model: {
    prop: 'dataList',
    event: 'change'
  },
  props: {
    // 用于初始化已选列表
    dataList: {
      type: Array,
      default: () => []
    },
    multiple: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: 'structure'
    }
  },
  data() {
    return {
      titleMap: {
        structure: '设施',
        device: '设备',
        deviceGroup: '设备组'
      },
      postMap: {
        structure: structureListPage,
        device: getDeviceList,
        deviceGroup: getDeviceGroupList
      },
      visible: false,
      listQuery: {
        params: {
          name: '',
          structureId: null,
          type: '',
          code: null,
          position: '',
          mileageValue: '',
          range: '',
          deviceParentTypeId: '',
          deviceTypeId: ''
        },
        pageSize: 10,
        currPage: 1
      },
      unitCode: {
        code: '',
        position: '',
        mileageValue: '',
        rangeQuery: ''
      },
      columns: [
        {
          text: '名称',
          value: 'name'
        },
        {
          text: '操作',
          type: 'button',
          width: 190,
          list: this.operButton
        }
      ],
      list: [],
      total: 0,
      showList: true,
      showCurrList: true,
      currentIndex: 0,
      selectedList: this.dataList.slice(0),
      currList: this.dataList.slice(0),
      dataName: this.dataList.map(item => item.name).join('、'),
      selectedTotal: 0,
      structureId: null,
      currPage: 1,
      selectedColumns: [
        {
          text: '名称',
          value: 'name'
        },
        {
          text: '操作',
          type: 'button',
          width: 190,
          list: this.selectedButton
        }
      ],
      deviceParentTypeList: [], // 设备大类列表
      deviceSearchTypeList: [] // 设备小类列表
    }
  },
  computed: {
    ...mapGetters(['dictMap']),
    isDevice() {
      return this.type === 'device'
    },
    isStructure() {
      return this.type === 'structure'
    },
    isDeviceGroup() {
      return this.type === 'deviceGroup'
    }
  },
  watch: {
    dataList() {
      this.selectedList = this.dataList.slice(0)
      this.updateList()
    },
    selectedList() {
      this.selectedTotal = this.selectedList.length
      const nameList = this.selectedList.map(item => item.name)
      this.dataName = nameList.join('、')
      this.updateList(this.currPage)
    }
  },
  created() {
  },
  mounted() {
    this.getDeviceTypeList()
    this.search()
  },
  methods: {

    changeSearchParentDeviceType(val) {
      this.deviceSearchTypeList = []
      this.listQuery.params.deviceTypeId = ''
      this.getDeviceTypeListByParent(val)
    },
    changeSearchDeviceType(val) {
    },
    // 获取所有设备类型
    getDeviceTypeList() {
      getDeviceTypeList({
        currPage: 1,
        pageSize: 2147483647,
        params: {}
      }).then(response => {
        if (response.success) {
          // 设备大类
          const dList = []
          response.result.list.forEach((item) => {
            if (!item.parentId) {
              dList.push(item)
            }
          })
          this.deviceParentTypeList = dList
          // 获取小类
          this.getDeviceTypeListByParent(dList[0].id)
        }
      })
    },
    getDeviceTypeListByParent(val) {
      getSonListByParentId({
        currPage: 1,
        pageSize: 2147483647,
        params: { id: val }
      }).then(response => {
        if (response.success) {
          this.deviceSearchTypeList = response.result
        }
      })
    },
    // 表格操作按鈕
    operButton(val) {
      let temp = []
      if (!val.isSelected) {
        temp = [{ class: 'icon-z', value: '选择', click: this.addStructure, type: 'text' }]
      }
      return temp
    },
    selectedButton(val) {
      const temp = [{ class: 'icon-z', value: '删除', click: this.deleteStructure, type: 'text' }]
      return temp
    },
    addStructure(val) {
      const find = this.selectedList.find(item => item.id === id)
      if (find) {
        this.$message.info('不能重复选择')
        return true
      }
      const id = val.row.id
      if (!this.multiple && this.selectedList.length === 1) {
        this.$message.info('只能选择一个')
        return
      }
      this.showList = false
      const structure = this.list.find(item => item.id === id)
      structure.isSelected = true
      this.selectedList.push(structure)
      this.$nextTick(() => {
        this.showList = true
      })
    },
    deleteStructure(val) {
      const index = val.$index
      this.selectedList.splice(index, 1)
      const temp = this.list.find(item => item.id === val.row.id)
      if (temp) {
        this.showList = false
        temp.isSelected = false
        this.$nextTick(() => {
          this.showList = true
        })
      }
    },
    cancel() {
      this.searchReset()
      this.visible = false
    },
    confirm() {
      this.$emit('change', this.selectedList)
      this.searchReset()
      this.visible = false
    },
    changeUnitCode() {
      this.listQuery.params.position = this.unitCode.position
      this.listQuery.params.mileageValue = this.unitCode.mileageValue
      this.listQuery.params.range = this.unitCode.rangeQuery
    },
    searchReset() {
      this.structureId = null
      this.listQuery.params.code = ''
      this.listQuery = {
        params: {
          structureId: null,
          type: null,
          code: null,
          position: '',
          mileageValue: '',
          range: '',
          deviceParentTypeId: '',
          deviceTypeId: ''
        },
        pageSize: 10,
        currPage: 1
      }
      this.unitCode = {
        code: '',
        position: '',
        mileageValue: '',
        rangeQuery: ''
      }
      this.selectedList = this.dataList.slice(0)
      this.list = []
      this.total = 0
    },
    search() {
      this.listQuery.currPage = 1
      //
      this.getList()
    },
    getList() {
      if (isNaN(this.structureId)) {
        this.listQuery.params.structureId = ''
      } else {
        this.listQuery.params.structureId = this.structureId
      }
      // const method = this.isStructure ? getStructureListByUnitCode : getDeviceListByUnitCode
      const method = this.postMap[this.type]
      let listQuery = { ...this.listQuery }
      if (this.isStructure) {
        listQuery = {
          pageSize: this.listQuery.pageSize,
          currPage: this.listQuery.currPage,
          params: { type: this.listQuery.params.type }
        }
      }
      method(listQuery).then(response => {
        if (response.success) {
          const list = response.result.list
          list.forEach(item => {
            if (this.selectedList.find(structure => structure.id === item.id)) {
              item.isSelected = true
            }
            if (this.isDeviceGroup) {
              item.name = item.name ? item.name : item.code
            }
          })
          this.list = list
          console.log(666, this.list)
          this.total = response.result.total
        }
      })
    },
    // getStructureList() {
    //   return new Promise(resolve => {
    //     getStructureListByUnitCode(this.listQuery).then(response => {
    //       if (response.success) {
    //         const list = response.result.list
    //         list.forEach(item => {
    //           if (this.selectedList.find(structure => structure.id === item.id)) {
    //             item.isSelected = true
    //           }
    //         })
    //         this.total = response.result.total
    //         resolve(list)
    //       }
    //     })
    //   })
    // },
    // getDeviceList() {
    //   return new Promise(resolve => {
    //     getDeviceListByUnitCode(this.listQuery).then(response => {
    //       if (response.success) {
    //         this.total = response.result.total
    //         resolve(response.result.list)
    //       }
    //     })
    //   })
    // },
    currentChange(val) {
      this.currentIndex = val.$index
      this.listQuery.currPage = val
      this.getList()
    },
    selectedChange(val) {
      this.currPage = val
      this.updateList(val)
    },
    updateList(val = 1) {
      if (val > 1 && ((val - 1) * 10) >= this.selectedTotal) {
        val = val - 1
        this.currPage = val - 1
      }
      this.showCurrList = false
      const endIndex = (val * 10 > this.selectedTotal) ? this.selectedTotal : val * 10
      this.currList = this.selectedList.slice(10 * (val - 1), endIndex)
      this.$nextTick(() => {
        this.showCurrList = true
      })
    },
    selectStructure() {
      this.visible = true
    }
  }
}
</script>

<style lang="scss">
.selectStructure {
  .el-dialog {
    width: 80%;
  }
  .arrowDiv {
    padding: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    height: 100%;
    div {
      height: 50%;
      cursor: pointer;
      font-size: 20px;
      line-height: 80px;
      &:nth-child(2) {
        transform: rotate(180deg);
      }
      &:hover {
      }
    }
  }
  .el-input__icon.el-input__validateIcon.el-icon-circle-close,
  .el-input__icon.el-input__validateIcon.el-icon-circle-check {
    display: none;
  }
}
</style>
