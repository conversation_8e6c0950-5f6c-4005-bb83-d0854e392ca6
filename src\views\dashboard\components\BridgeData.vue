<!-- description -->
<template>
  <div class="bridge-wrapper">
    <div class="title-wrapper">
      <title-row :title="title"></title-row>
    </div>
    <div class="content-wrapper">
      <div class="content-row">
        <div class="row-name">资产</div>
        <div class="charts-wrapper">
          <div class="chart-container">
            <pie-chart
              :total="structureTotal"
              sub-text="设施"
              :value="structureData"/>
          </div>
          <div class="chart-container">
            <pie-chart
              :total="deviceTotal"
              sub-text="设备"
              :value="deviceData"/>
          </div>
        </div>
      </div>
      <div class="content-row">
        <div class="row-name">报警</div>
        <div class="charts-wrapper">
          <div class="chart-container">
            <pie-chart
              :total="alertTotal"
              :sub-text="'总数'"
              :value="alertTotalData">
            </pie-chart>
          </div>
          <div class="chart-container">
            <pie-chart
              :total="alertUnprocessed"
              sub-text="未处理"
              :value="alertUnprocessedData">
            </pie-chart>
          </div>
        </div>
      </div>
      <div class="content-row">
        <div class="row-name">缺陷</div>
        <div class="charts-wrapper">
          <div class="chart-container">
            <pie-chart
              :total="defectTotal"
              :sub-text="'总数'"
              :value="defectData">
            </pie-chart>
          </div>
          <div class="chart-container">
            <pie-chart
              :total="defectUnprocessed"
              :sub-text="'未处理'"
              :value="defectData">
            </pie-chart>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import TitleRow from './TitleRow'
  import PieChart from './PieChart'
  import { getChartDataByKeys } from '../../../utils'
  export default {
    name: 'BridgeData',
    components: { PieChart, TitleRow },
    directives: {},
    props: {
      bridgeData: {
        type: Object,
        default: () => {}
      }
    },
    data() {
      return {
        title: '世纪大桥（斜拉桥）',
        structureData: [
          { name: '完好', value: 0, code: 'intact' },
          { name: '有缺陷', value: 0, code: 'defect' },
          { name: '有报警', value: 0, code: 'alert' }
        ],
        deviceData: [],
        alertTotalData: [
          { name: '一级', value: 0, code: 'level1' },
          { name: '二级', value: 0, code: 'level2' }
        ],
        alertUnprocessedData: [
          { name: '一级', value: 0, code: 'level1' },
          { name: '二级', value: 0, code: 'level2' }
        ],
        defectData: [
          { name: '一级', value: 0, code: 'level1' },
          { name: '二级', value: 0, code: 'level2' },
          { name: '三级', value: 0, code: 'level3' },
          { name: '四级', value: 0, code: 'level4' }
        ],
        defectUnprocessedData: [],
        structureTotal: 0,
        deviceTotal: 0,
        alertTotal: 0,
        alertUnprocessed: 0,
        defectTotal: 0,
        defectUnprocessed: 0
      }
    },
    computed: {},
    created() {
      this.title = this.bridgeData ? this.bridgeData.detailAddress || this.bridgeData.name || '' : ''
      this.structureData = this.structureData.map(item => {
        const temp = { ...item }
        temp.value = this.bridgeData.structure[item.code]
        return temp
      })
      this.structureTotal = this.bridgeData.structure.count
      this.deviceData = this.structureData.map(item => {
        const temp = { ...item }
        temp.value = this.bridgeData.device[item.code]
        return temp
      })
      this.deviceTotal = this.bridgeData.device.count

      if (this.bridgeData.defectAll) {
        this.defectData = getChartDataByKeys(this.defectData, this.bridgeData.defectAll)
        this.defectTotal = this.bridgeData.defectAll.count
      }
      if (this.bridgeData.defectUntreated) {
        this.defectUnprocessedData = getChartDataByKeys(this.defectData, this.bridgeData.defectUntreated)
        this.defectUnprocessed = this.bridgeData.defectUntreated.count
      }

      if (this.bridgeData.alertAll) {
        this.alertTotalData = getChartDataByKeys(this.alertTotalData, this.bridgeData.alertAll)
        this.alertTotal = this.bridgeData.alertAll.count
      }
      if (this.bridgeData.alertUntreated) {
        this.alertUnprocessedData = getChartDataByKeys(this.alertTotalData, this.bridgeData.alertUntreated)
        this.alertUnprocessed = this.bridgeData.alertUntreated.count
      }
    },
    mounted() {
    },
    methods: {}
  }
</script>

<style rel="stylesheet/scss" lang="scss">
.bridge-wrapper {
  .title-wrapper {
    padding-top: 10px;
    padding-left: 20px;
    border-bottom: 1px solid rgba(197, 208, 215, 1);
  }
  .content-wrapper {
    margin: 5px 10px;
    width: calc(100% - 20px);
    min-height: 360px;
    border: 1px solid rgba(197, 208, 215, 1);
    .content-row {
      display: flex;
      height: 120px;
      border-bottom: 1px solid rgba(240, 243, 245, 1);
      .row-name {
        font-size: 14px;
        font-weight: bold;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 50px;
        color: rgba(51, 97, 143, 1);
        background: rgba(197, 208, 215, .24);
      }
      .charts-wrapper {
        width: calc(100% - 50px);
        height: 100%;
        display: flex;
        justify-content: space-around;
        .chart-container {
          width: 45%;
        }
      }
    }
  }
}
</style>
