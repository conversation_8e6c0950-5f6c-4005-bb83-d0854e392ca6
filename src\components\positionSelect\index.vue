<template>
  <div class="mapDiv">
    <el-button
      class="filter-item"
      size="mini"
      type="primary"
      @click="visible = true"
    >{{ displayText }}</el-button
    >
    <el-dialog :visible.sync="visible" title="选取坐标" append-to-body>
      <el-form
        ref="positionForm"
        :rules="rules"
        :model="positionForm"
        label-position="center"
        size="small"
        label-width="100px"
      >
        <el-row>
          <el-col :span="8">
            <el-form-item label="省" prop="provinceId">
              <el-select
                v-model="positionForm.provinceId"
                filterable
                placeholder="请选择省"
                @change="changeProvince"
              >
                <el-option
                  v-for="item in provinceList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="市" prop="cityId">
              <el-select
                v-model="positionForm.cityId"
                filterable
                placeholder="请选择市"
                @change="changeCity"
              >
                <el-option
                  v-for="item in cityList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="区/县" prop="countyId">
              <el-select
                v-model="positionForm.countyId"
                filterable
                placeholder="请选择区/县"
                @change="changeCounty"
              >
                <el-option
                  v-for="item in countyList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="街道" prop="streetId">
              <el-select
                v-model="positionForm.streetId"
                filterable
                placeholder="请选择街道"
                @change="changeStreet"
              >
                <el-option
                  v-for="item in streetList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="详细地址" prop="detailAddress">
              <el-input
                v-model="positionForm.detailAddress"
                type="text"
                class="filter-item"
                maxlength="20"
                placeholder="请输入详细地址"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <div id="container" style="height: 300px;" />
      <div slot="footer" class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" @click="surePosition">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
// import AMap from 'AMap'
import { getRootAreaList, getArea } from '@/api/location'

export default {
  name: 'PositionSelect',
  props: {
    location: {
      type: Object,
      default: () => {}
    },
    isChoose: { type: Boolean, default: false }
  },
  data() {
    return {
      map: null,
      marker: null,
      visible: false,
      displayText: '未选取',
      positionForm: {
        provinceId: '',
        cityId: '',
        countyId: '',
        streetId: '',
        detailAddress: '',
        address: '',
        longitude: '',
        latitude: ''
      },
      currentProvinceName: '',
      currentCityName: '',
      currentCountyName: '',
      currentStreetName: '',
      provinceList: [],
      cityList: [],
      countyList: [],
      streetList: [],
      rules: {
        provinceId: [
          { required: true, message: '请选择省', trigger: 'change' }
        ],
        cityId: [{ required: true, message: '请选择市', trigger: 'change' }],
        countyId: [
          { required: true, message: '请选择区/县', trigger: 'change' }
        ],
        streetId: [
          { required: true, message: '请选择街道', trigger: 'change' }
        ],
        detailAddress: [{ required: true, message: '请填写详细地址' }]
      }
    }
  },
  watch: {
    visible(val) {
      if (val && !this.map) {
        this.$nextTick(() => {
          this.init()
        })
      }
    },
    isChoose(val) {
      this.displayText = val ? '已选取' : '未选取'
    },
    location(val) {
      const proto = Object.getPrototypeOf(val)
      this.positionForm = Object.assign({}, Object.create(proto), val)
      this.$nextTick(() => {
        this.init()
      })
    }
    // 'positionForm.provinceId': function(val) {
    //   if (val && val !== '') {
    //     // 获取城市
    //     this.getCity(val)
    //   }
    // },
    // 'positionForm.cityId': function(val) {
    //   if (val && val !== '') {
    //     // 获取区县
    //     this.getCounty(val)
    //   }
    // },
    // 'positionForm.countyId': function(val) {
    //   if (val && val !== '') {
    //     // 获取街道
    //     this.getStreet(val)
    //   }
    // }
  },
  mounted() {
    this.getProvince()
    this.displayText = this.isChoose ? '已选取' : '未选取'
    const proto = Object.getPrototypeOf(this.location)
    this.positionForm = Object.assign({}, Object.create(proto), this.location)
  },
  methods: {
    init: function() {
      const me = this
      const config = { resizeEnable: true, zoom: 5 }
      if (me.positionForm.longitude && me.positionForm.longitude !== '') {
        config.center = [me.positionForm.longitude, me.positionForm.latitude]
        config.zoom = 12
        this.getCity(me.positionForm.provinceId)
        this.getCounty(me.positionForm.cityId)
      }
      this.map = new AMap.Map('container', config)
      this.map.on('complete', function() {
        if (me.positionForm.longitude && me.positionForm.longitude !== '') {
          me.addMarker([me.positionForm.longitude, me.positionForm.latitude])
        } else {
          me.addMarker(me.map.getCenter())
        }
      })
      // AMap.plugin(['AMap.ToolBar', 'AMap.Scale'], function() {
      //   me.map.addControl(new AMap.ToolBar())
      //   me.map.addControl(new AMap.Scale())
      // })
    },
    changeProvince(val) {
      const me = this
      this.currentProvinceName = this.provinceList.find(item => {
        return item.id === val
      }).name
      // this.cityList = []
      // this.positionForm.cityId = ''
      // this.countyList = []
      // this.positionForm.countyId = ''
      // this.streetList = []
      // this.positionForm.streetId = ''
      this.map.setCity(this.currentProvinceName, function() {
        me.addMarker(me.map.getCenter())
      })
      // 获取城市
      this.getCity(val)
    },
    changeCity(val) {
      const me = this
      this.currentCityName = this.cityList.find(item => {
        return item.id === val
      }).name
      // this.countyList = []
      // this.positionForm.countyId =''
      // this.streetList = []
      // this.positionForm.streetId = ''
      this.map.setCity(this.currentCityName, function() {
        me.addMarker(me.map.getCenter())
      })
      // 获取区县
      this.getCounty(val)
    },
    changeCounty(val) {
      const me = this
      this.currentCountyName = this.countyList.find(item => {
        return item.id === val
      }).name
      // this.streetList = []
      // this.positionForm.streetId = ''
      // this.map.setCity(this.currentCountyName, function() {
      //   me.addMarker(me.map.getCenter())
      // })
      me.getStreetPosition()
      // 获取街道
      this.getStreet(val)
    },
    changeStreet(val) {
      const me = this
      this.currentStreetName = this.streetList.find(item => {
        return item.id === val
      }).name
      me.getStreetPosition()
      // me.addMarker()
      // this.map.setCity(this.currentStreetName, function() {
      //   me.addMarker(me.map.getCenter())
      // })
      me.$forceUpdate()
    },
    getProvince() {
      getRootAreaList().then(response => {
        if (response.success && response.result && response.result.length > 0) {
          this.provinceList = response.result
        }
      })
    },
    getCity(provinceId) {
      getArea({ id: provinceId }).then(response => {
        if (response.success && response.result && response.result.length > 0) {
          this.cityList = response.result
          this.positionForm.cityId = response.result[0].id
          if (this.positionForm.cityId) {
            this.getCounty(this.positionForm.cityId)
          }
        } else {
          this.positionForm.cityId = '暂无数据'
          this.positionForm.countyId = '暂无数据'
          this.positionForm.streetId = '暂无数据'
          this.cityList = []
          this.countyList = []
          this.streetList = []
          this.detailAddress = ''
          this.$message.error('无对应下级选项')
        }
      })
    },
    getCounty(cityId) {
      getArea({ id: cityId }).then(response => {
        if (response.success && response.result && response.result.length > 0) {
          this.countyList = response.result
          this.positionForm.countyId = response.result[0].id
          if (this.positionForm.countyId) {
            this.getStreet(this.positionForm.countyId)
          }
        } else {
          this.positionForm.countyId = '暂无数据'
          this.positionForm.streetId = '暂无数据'
          this.countyList = []
          this.streetList = []
          this.$message.error('无对应下级选项')
        }
      })
    },
    getStreet(countyId) {
      getArea({ id: countyId }).then(response => {
        if (response.success && response.result && response.result.length > 0) {
          this.streetList = response.result
          this.positionForm.streetId = response.result[0].id
        } else {
          this.positionForm.streetId = '暂无数据'
          this.streetList = []
          this.$message.error('无对应下级选项')
        }
      })
    },
    // 添加覆盖物
    addMarker(position) {
      if (this.marker) {
        this.map.remove(this.marker)
        this.marker = null
      }
      this.marker = new AMap.Marker({
        position: position,
        draggable: true,
        cursor: 'move',
        raiseOnDrag: true
      })
      this.marker.setMap(this.map)
    },
    surePosition() {
      this.$refs['positionForm'].validate(valid => {
        if (valid) {
          const isNoFull =
            this.positionForm.cityId !== '暂无数据' &&
            this.positionForm.countyId !== '暂无数据' &&
            this.positionForm.streetId !== '暂无数据'
          if (!isNoFull) {
            this.$message.error('未填写完整')
            return
          }
          this.positionForm.address =
            this.currentProvinceName +
            this.currentCityName +
            this.currentCountyName +
            this.currentStreetName +
            this.positionForm.detailAddress
          this.positionForm.longitude = this.marker.getPosition().lng
          this.positionForm.latitude = this.marker.getPosition().lat
          this.$emit('getLocation', this.positionForm)
          this.displayText = '已选取'
          this.visible = false
        }
      })
    },
    getStreetPosition() {
      this.$forceUpdate()
      const me = this
      const locationDetail =
        (me.currentProvinceName === undefined ? '' : me.currentProvinceName) +
        (me.currentCityName === undefined ? '' : me.currentCityName) +
        (me.currentCountyName === undefined ? '' : me.currentCountyName) +
        (me.currentStreetName === undefined ? '' : me.currentStreetName)
      AMap.plugin('AMap.Geocoder', function() {
        const geocoder = new AMap.Geocoder({})
        geocoder.getLocation(locationDetail, function(status, result) {
          if (status === 'complete' && result.info === 'OK') {
            const position = [
              result.geocodes[0].location.lng,
              result.geocodes[0].location.lat
            ]
            me.map.setZoomAndCenter(22, position)
            me.addMarker(position)
          } else {
            me.$message.error('根据地址查询位置失败')
            return
          }
        })
      })
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss"></style>
