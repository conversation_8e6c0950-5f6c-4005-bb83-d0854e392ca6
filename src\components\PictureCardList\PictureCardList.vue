<template>
  <div>
    <el-upload
      :name="name"
      :file-list="fileList"
      :auto-upload="autoUpload"
      :action="action"
      :headers="headers"
      :multiple="multiple"
      :class="isView ? 'isView' : ''"
      ref="pictureCard"
      list-type="picture-card"
      class="picture-card-list"
      style="margin-bottom: 20px;margin-left: 60px"
      size="mini"
      accept=".jpg,.jpeg,.png,.gif,.bmp,.JPG,.JPEG,.PNG,.GIF,.BMP"
      :http-request="upload"
      :on-remove="removePicture"
      :on-success="handleSuccess"
      :before-remove="beforeRemove"
      :on-preview="handleImgPreview">
      <i class="el-icon-plus"></i>
    </el-upload>
    <el-image-viewer
      v-if="innerVisible"
      :url-list="urlList"
      :on-close="closeViewer"
      :initial-index="currentIndex"
    ></el-image-viewer>
  </div>
</template>
<script>
  import { mapGetters } from 'vuex'
  /**
   * 上传多个图片组件
   * @module PictureCardList
   * @date 2020/4/7
   * <AUTHOR>
   */
  import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
  export default {
    name: 'PictureCardList',
    /**
     * Props
     * @prop {Boolean} isView 是否查看状态 默认false
     * @prop {Array} fileList 绑定文件列表
     */
    components: {
      ElImageViewer
    },
    props: {
      isView: {
        type: Boolean,
        default: false
      },
      fileList: {
        type: Array,
        default: () => []
      },
      autoUpload: {
        type: Boolean,
        default: false
      },
      multiple: {
        type: Boolean,
        default: true
      },
      action: {
        type: String,
        default: ''
      },
      headers: {
        type: Object,
        default: () => {}
      },
      name: {
        type: String,
        default: ''
      }
    },
    data() {
      return {
        dialogImageUrl: '', // 查看大图url
        innerVisible: false, // 查看大图窗口
        currentIndex: 0 // 当前图片索引
      }
    },
    computed: {
      ...mapGetters(['fileUrl']),
      isFirst() {
        return this.currentIndex === 0
      },
      isLast() {
        return this.currentIndex === this.fileList.length - 1
      },
      urlList() {
        return this.fileList.map(item => item.url)
      }
    },
    methods: {
      submit() {
        this.$refs['pictureCard'].submit()
      },
      upload(item) {
        this.$emit('onUpload', item)
      },
      removePicture(file) {
        this.$emit('onRemove', file)
      },
      handleSuccess(res) {
        // todo 这里好像有点问题 自动上传获取不到
        this.$emit('onSuccess', res)
      },
      handleImgPreview(file) {
        this.currentIndex = this.fileList.indexOf(file)
        this.dialogImageUrl = file.url
        this.innerVisible = true
      },
      beforeRemove() {
        return !this.isView
      },
      // 关闭viewer
      closeViewer() {
        this.innerVisible = false
      }
    }
  }
</script>

<style rel="stylesheet/scss" lang="scss">
  .picture-card-list {
    // 查看状态 隐藏添加删除按钮
    &.isView {
      .el-upload.el-upload--picture-card {
        display: none !important;
      }
      .el-upload-list__item-delete,
      .el-icon-close,
      .el-icon-close-tip,
      .el-upload-list__item-status-label {
        display: none !important;
      }
    }
  }
</style>
