import request from '@/utils/request'

const list = [
  { name: 'windLoad' }, // 风荷载
  { name: 'scatterPlot' }, // 风荷载
  { name: 'boxPlot' }, // 盒须图
  { name: 'trafficStatistics' }
]
const basePath = 'rest/data/'
const dataProvider = {}

list.forEach(item => {
  dataProvider[item.name] = function(param) {
    return request({
      url: `${basePath}${item.name}`,
      method: item.method || 'post',
      data: param
    })
  }
})

export default dataProvider

