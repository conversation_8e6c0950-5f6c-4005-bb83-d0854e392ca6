<!--数据监测-->
<template>
  <el-row>
    <el-col :span="12">
      <el-tree v-bind:style="[treeHeight]"
               :props="props"
               :load="loadNode1"
               @node-click="nodeClick"
               :highlight-current="true"
               style="overflow: auto;margin-right: 20px;"
               :expand-on-click-node="false"
               lazy>
        <span class="custom-tree-node" slot-scope="{ node, data }">
          <i class="icon iconfont icon-ccgl-xiangmuguanli-3" v-if="data.type === 'project'"></i>
          <i class="icon iconfont icon-shebei" v-if="data.type === 'device'"></i>
          <i class="icon iconfont icon-zuzhi" v-if="data.type === 'structure'"></i>
          <i class="icon iconfont icon-cedian" v-if=" data.type === 'channel'"></i>
          <span>{{ node.label }}</span>
        </span>
      </el-tree>
    </el-col>
    <el-col :span="12">
      <el-card>
        <div slot="header" class="clearfix">
          <span>已选测点</span>
        </div>
        <table-list :columns="selectedColumns"
                    v-if="showCurrList"
                    class="dataTable"
                    :page-size="10"
                    :page-num="currPage"
                    @currentChange="selectedChange"
                    :data="currList"
                    :total="selectedTotal"></table-list>
      </el-card>
    </el-col>
  </el-row>
</template>

<script>
  import waves from '@/directive/waves'
  import TableList from '@/components/table/tableList.vue'
  import { getAsynTreeStructure } from '@/api/project/project'
  /**
   * 选择测点变量组件
   * @module SelectChannel
   */
  export default {
    name: 'SelectChannel',
    /**
     * @prop {Component} TableList 表格组件
     */
    components: {
      TableList
    },
    directives: {
      waves
    },
    model: {
      prop: 'channelList',
      event: 'change'
    },
    /**
     * Props
     * @prop {Array} channelList v-model绑定值，变量列表
     * @prop {Boolean} multiple 是否多选
     * @prop {String} targetClass 外层元素的类名
     * @prop {Boolean} isProject 是否是单项目
     */
    props: {
      channelList: {
        type: Array,
        default: () => []
      },
      multiple: {
        type: Boolean,
        default: false
      },
      targetClass: {
        type: String,
        default: ''
      },
      isProject: {
        type: Boolean,
        default: true
      }
    },
    data() {
      return {
        // 树结构
        props: {
          label: 'name',
          children: 'children',
          isLeaf: 'leaf'
        },
        treeHeight: null,
        myTime: [new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate(), 0, 0, 0), new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate() + 1, 0, 0, 0)],
        listQuery: {
          start: '',
          end: '',
          id: ''
        },
        currentPoint: {},
        loading: false,
        selectedColumns: [
          {
            text: '设备名称',
            value: 'deviceName'
          },
          {
            text: '测点名称',
            value: 'name'
          },
          {
            text: '操作',
            type: 'textButton',
            width: 190,
            list: this.selectedButton
          }
        ],
        showList: true,
        showCurrList: true,
        currPage: 1,
        selectedTotal: 0,
        selectedList: this.channelList.slice(0),
        currList: this.channelList.slice(0)
      }
    },
    watch: {
      channelList() {
        this.selectedList = this.channelList.slice(0)
      },
      selectedList() {
        this.selectedTotal = this.selectedList.length
        this.updateList(this.currPage)
      }
    },
    mounted() {
      this.treeHeight = { height: document.getElementsByClassName(this.targetClass)[0].offsetHeight - 40 + 'px' }
    },
    methods: {
      loadNode1(node, resolve) {
        if (node.level === 0) {
          getAsynTreeStructure({ type: 'root' }).then(response => {
            if (response.success) {
              if (this.isProject) {
                const projectId = JSON.parse(this.$storage.getStorage('project')).id
                response.result = response.result.filter(item => item.id === projectId)
              }
              return resolve(response.result)
            }
          })
        } else {
          getAsynTreeStructure({ id: node.data.id, type: node.data.type }).then(response => {
            if (response.success) {
              return resolve(response.result)
            }
          })
        }
      },
      // 节点点击事件，只有测点才调用接口
      nodeClick(data, Node) {
        if (data.type !== 'channel') {
          return
        }
        if (this.selectedList.length > 0 && !this.multiple) {
          this.$message.error('只能选择一个')
          return
        }
        const device = { deviceName: Node.parent.label, deviceId: Node.parent.data.id }
        const channel = Object.assign({}, data, device)
        this.selectedList.push(channel)
        this.$emit('change', this.selectedList)
      },
      selectedButton(val) {
        const temp = [{ class: 'icon-z', value: '删除', click: this.deleteChannel }]
        return temp
      },
      deleteChannel(val) {
        const index = val.$index
        this.selectedList.splice(index, 1)
        this.$emit('change', this.selectedList)
      },
      selectedChange(val) {
        this.currPage = val
        this.updateList(val)
      },
      updateList(val = 1) {
        if (val > 1 && ((val - 1) * 10) >= this.selectedTotal) {
          val = val - 1
          this.currPage = val - 1
        }
        this.showCurrList = false
        const endIndex = (val * 10 > this.selectedTotal) ? this.selectedTotal : val * 10
        this.currList = this.selectedList.slice(10 * (val - 1), endIndex)
        this.$nextTick(() => {
          this.showCurrList = true
        })
      }
    }
  }
</script>
