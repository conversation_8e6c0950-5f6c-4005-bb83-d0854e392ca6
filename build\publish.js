const scpClient = require('scp2');
const ora = require('ora');
const chalk = require('chalk');
const server = require('../config');
const spinner = ora('正在发布到服务器...');
spinner.start();
scpClient.scp(
  './dist',
  {
    host: process.env.deploy_config == 'dev' ? server.publishDev.serverConfig.host : server.publishProd.serverConfig.host,
    port: process.env.deploy_config == 'dev' ? server.publishDev.serverConfig.port : server.publishProd.serverConfig.port,
    username: process.env.deploy_config == 'dev' ? server.publishDev.serverConfig.username : server.publishProd.serverConfig.username,
    password: process.env.deploy_config == 'dev' ? server.publishDev.serverConfig.password : server.publishProd.serverConfig.password,
    path: process.env.deploy_config == 'dev' ? server.publishDev.remoteDir : server.publishProd.remoteDir
  },
  function (err) {
    spinner.stop();
    if (err) {
      console.log(chalk.red('发布失败.\n'));
      throw err;
    } else {
      console.log(chalk.green('Success! 成功发布到服务器!'));
    }
  }
);
