import { getStructurePath, structureTreeData } from '@/api/project/project'

export const aysnTree = {
  methods: {
    initPath(id, temp) {
      return new Promise(async resolve => {
        temp.forEach(item => {
          item.entity.parentId = 0
        })
        const pathList = await this.getStructurePath(id)
        for (const item of pathList) {
          const itemArr = await this.structureTreeData(item.id, 'structure')
          temp = [...temp, ...itemArr]
        }
        resolve(this.treeListUtil(temp, 0))
      })
    },
    treeListUtil(data, parentId) {
      const itemArr = []
      for (let i = 0; i < data.length; i++) {
        const node = data[i]
        if (node.entity.parentId === parentId) {
          if (this.treeListUtil(data, node.id).length > 0) {
            node.children = this.treeListUtil(data, node.id)
          }
          itemArr.push(node)
        }
      }
      return itemArr
    },
    getStructurePath(id) {
      return new Promise((resolve) => {
        setTimeout(() => {
          getStructurePath({ structureId: id }).then(res => {
            if (res.success) {
              resolve(res.result)
            }
          })
        })
      })
    },
    structureTreeData(id, type) {
      return new Promise((resolve) => {
        setTimeout(() => {
          structureTreeData({ id: id, type: type }).then(response => {
            if (response.success) {
              response.result.forEach(item => {
                if (!item.leaf) {
                  item.children = null
                }
              })
              resolve(response.result)
            }
          })
        })
      })
    }
  }
}
