import request from '@/utils/request'

export function getProductList(param) {
  return request({
    url: 'rest/manufacturer/productList',
    method: 'post',
    data: param
  })
}

export function deleteProduct(param) {
  return request({
    url: 'rest/manufacturer/deleteProduct',
    method: 'post',
    data: param
  })
}

export function saveProduct(param) {
  return request({
    url: 'rest/manufacturer/saveProduct',
    method: 'post',
    data: param
  })
}

