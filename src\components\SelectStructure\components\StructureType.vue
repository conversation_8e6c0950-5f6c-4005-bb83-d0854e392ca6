<template>
  <el-select class="filter-item structure-type-select"
             style="width: 160px"
             placeholder="请选择设施类型"
             v-model="code"
             clearable
             filterable
             @change="$emit('change', code)">
    <el-option-group
      v-for="group in typeOptions"
      :key="group.label"
      :label="group.label">
      <el-option
        v-for="item in group.options"
        :key="item.value"
        :label="item.label"
        :value="item.value">
      </el-option>
    </el-option-group>
  </el-select>
</template>

<script>
  import { mapGetters } from 'vuex'
  export default {
    name: 'StructureType',
    model: {
      prop: 'typeCode',
      event: 'change'
    },
    props: {
      typeCode: {
        type: String,
        default: ''
      }
    },
    data() {
      return {
        code: this.typeCode,
        typeOptions: [],
        structureTypeList: [],
        typeLetterMap: {}
      }
    },
    computed: {
      ...mapGetters(['dictMap'])
    },
    watch: {
      typeCode() {
        this.code = this.typeCode
      }
    },
    mounted() {
      this.structureTypeList = this.dictMap['S6SSLX']
      this.structureTypeList.forEach(item => {
        item.value = item.code
        item.label = item.name
        const letter = item.code[0]
        if (!this.typeLetterMap[letter]) {
          this.typeLetterMap[letter] = { label: letter, options: [] }
        }
        this.typeLetterMap[letter].options.push(item)
      })
      for (const key in this.typeLetterMap) {
        this.typeOptions.push(this.typeLetterMap[key])
      }
      function letterSort(a, b) {
        const val1 = a.label
        const val2 = b.label
        if (val1 < val2) {
          return -1
        } else if (val1 > val2) {
          return 1
        } else {
          return 0
        }
      }
      this.typeOptions.sort(letterSort)
    },
    methods: {
    }
  }
</script>

<style lang="scss">
  .structure-type-select {
    .el-input__icon.el-input__validateIcon.el-icon-circle-close,
    .el-input__icon.el-input__validateIcon.el-icon-circle-check{
      display: none;
    }
  }
</style>
