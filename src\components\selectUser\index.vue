<template>
  <div class="selectUser">
    <treeselect v-model="userId" :options="userList"
                :normalizer="normalizer" placeholder="请选择人员" :multiple="isMultiple" value-consists-of="LEAF_PRIORITY"
                no-children-text="无选择" @input="getValue" @select="getSelect">
      <label slot="option-label" slot-scope="{ node, shouldShowCount, count, labelClassName, countClassName }"
             :class="labelClassName">
        <span v-if="node.raw.type=='org'"><i class="icon iconfont icon-zuzhi"
                                             style="color:#409EFF;margin-right: 5px"></i>{{ node.label }}</span>
        <span v-else><i class="icon iconfont icon-yonghu1"
                        style="color:#409EFF;margin-right: 5px"></i>{{ node.label }}</span>
      </label>
    </treeselect>
  </div>
</template>

<script>
  import Treeselect from '@riophae/vue-treeselect'
  import '@riophae/vue-treeselect/dist/vue-treeselect.css'
  import { userTreeData } from '@/api/system/person'
  import { mapGetters } from 'vuex'
  export default {
    name: 'SelectUser',
    components: { Treeselect },
    props: {
      userIds: {
        type: Array,
        default() {
          return ''
        }
      },
      isMultiple: {
        type: Boolean,
        default: true
      }
    },
    data() {
      return {
        userList: [],
        userId: null,
        normalizer(node) {
          if (node.type === 'org') {
            return {
              id: node.id,
              label: node.name,
              children: node.children,
              type: node.type
            }
          } else {
            return {
              id: node.id,
              label: node.name,
              children: node.children,
              type: node.type
            }
          }
        }
      }
    },
    computed: {
      ...mapGetters([
        'projectOrgId'
      ])
    },
    watch: {
      userIds(val) {
        const temp = []
        val.forEach(function(item) {
          temp.push('user' + item)
        })
        if (temp.length === 0) {
          this.userId = null
        } else {
          this.userId = temp
        }
      },
      projectOrgId(val) {
        if (val) {
          this.getUser()
        }
      }
    },
    mounted() {
      this.getUser()
    },
    methods: {
      getSelect(e) {
        this.$emit('getSelect', e)
      },
      getValue() {
        const me = this
        if (!this.userId) {
          this.$emit('update:userIds', [])
          return false
        }
        if (!this.isMultiple) {
          if (Array.isArray(this.userId)) {
            return
          }
          if (this.userId.toString().indexOf('user') === -1) {
            setTimeout(function() {
              me.userId = null
            })
            return false
          }
          const id = this.userId.substr(4, this.userId.length)
          this.$emit('update:userIds', [id])
          return false
        }
        const ids = []
        this.userId.forEach(function(item) {
          ids.push(item.substr(4, item.length))
        })
        this.$emit('update:userIds', ids)
      },
      getUser() {
        userTreeData({ orgId: this.projectOrgId }).then(response => {
          if (response.success) {
            // 过滤根组织
            if (response.result && response.result.length > 0) {
              response.result.forEach(item => {
                if (item.parentId) {
                  if (!response.result.find(res => res.id === item.parentId)) {
                    delete item['parentId']
                  }
                }
              })
            }
            // 组装参数
            this.userList = this.treeListUtil(response.result)
            const temp = []
            if (this.userIds) {
              this.userIds.forEach(function(item) {
                temp.push('user' + item)
              })
            }
            if (!this.isMultiple) {
              this.userId = temp[0]
            } else {
              this.userId = temp
            }
          }
        })
      },
      treeListUtil(data, parentId) {
        const itemArr = []
        for (let i = 0; i < data.length; i++) {
          const node = data[i]
          if (node.parentId === parentId) {
            if (node.type === 'user') {
              if ((node.id.toString()).indexOf('user') === -1) {
                node.id = node.type + node.id
                if (node.status === 'false') {
                  node.isDisabled = true
                }
              }
            } else {
              if (this.treeListUtil(data, node.id).length > 0) {
                node.children = this.treeListUtil(data, node.id)
              } else {
                node.isDisabled = true
              }
            }
            itemArr.push(node)
          }
        }
        return itemArr
      }
    }
  }
</script>

<style scoped>
</style>
