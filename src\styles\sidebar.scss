#app {
  // 主体区域
  .main-container {
    min-height: 100%;
    transition: margin-left .28s;
    margin-left: 180px
  }
  // 侧边栏
  .sidebar-container {
    transition: width .28s;
    width: 180px !important;
    height: calc(100% - 48px);
    position: absolute;
    font-size: 0px;
    top: 0px;
    margin-top: 48px;
    bottom: 0;
    left: 0;
    z-index: 1001;
    overflow: hidden;
    a {
      display: inline-block;
      width: 100%;
    }
    .svg-icon {
      margin-right: 16px;
    }
    .el-menu {
      border: none;
      width: 100% !important;
    }
  }
  .hideSidebar {
    .sidebar-container {
      width: 36px !important;
    }
    .main-container {
      margin-left: 36px;
    }
    .submenu-title-noDropdown {
      padding-left: 10px !important;
      position: relative;
      .el-tooltip {
        padding: 0 10px !important;
      }
    }
    .el-submenu {
      &>.el-submenu__title {
        padding-left: 10px !important;
        &>span {
          height: 0;
          width: 0;
          overflow: hidden;
          visibility: hidden;
          display: inline-block;
        }
        .el-submenu__icon-arrow {
          display: none;
        }
      }
    }
  }
  .nest-menu .el-submenu>.el-submenu__title,
  .el-submenu .el-menu-item {
    min-width: 180px !important;
    background-color: $subMenuBg !important;
    &:hover {
      background-color: $menuHover !important;
    }
  }
  .el-menu--collapse .el-menu .el-submenu {
    min-width: 180px !important;
  }
}
