<!-- description -->
<template>
  <div class="bridge-statistics">
    <bridge-data
      v-for="(item, index) in list"
      :key="index"
      :bridge-data="item">
    </bridge-data>
    <!--    <bridge-data />-->
    <!--    <bridge-data />-->
  </div>
</template>

<script>
  import BridgeData from './BridgeData'
  export default {
    name: 'BridgeStatistics',
    components: { BridgeData },
    directives: {},
    props: {
      list: {
        type: Array,
        default: () => []
      }
    },
    data() {
      return {}
    },
    computed: {},
    created() {
    },
    mounted() {},
    methods: {

    }
  }
</script>

<style rel="stylesheet/scss" lang="scss">
.bridge-statistics {
  position: absolute;
  right: 0;
  top: 50px;
  height: calc(100% - 60px);
  width: 500px;
  background: white;
}
</style>
