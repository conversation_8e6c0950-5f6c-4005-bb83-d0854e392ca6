import request from '@/utils/request'

export function getSecurityEvaluationList(param) {
  return request({
    url: 'rest/securityEvaluation/list',
    method: 'post',
    data: param
  })
}

export function deleteSecurityEvaluation(param) {
  return request({
    url: 'rest/securityEvaluation/delete',
    method: 'post',
    data: param
  })
}

export function saveSecurityEvaluation(param) {
  return request({
    url: 'rest/securityEvaluation/save',
    method: 'post',
    data: param
  })
}

export function getSecurityEvaluation(param) {
  return request({
    url: 'rest/securityEvaluation/get',
    method: 'post',
    data: param
  })
}

export function getAttach(param) {
  return request({
    url: 'rest/doc/attachList',
    method: 'post',
    data: param
  })
}
