import request from '@/utils/request'

export function getChannelList(param) {
  return request({
    url: 'rest/channel/list',
    method: 'post',
    data: param
  })
}

export function deleteChannel(param) {
  return request({
    url: 'rest/channel/delete',
    method: 'post',
    data: param
  })
}

export function saveChannel(param) {
  return request({
    url: 'rest/channel/save',
    method: 'post',
    data: param
  })
}

export function getOneChannel(param) {
  return request({
    url: 'rest/channel/get',
    method: 'post',
    data: param
  })
}

// 查询报警设备
export function getAlertDeviceList(param) {
  return request({
    url: 'rest/channel/getAlertDeviceList',
    method: 'post',
    data: param
  })
}

// 查询报警设施
export function getAlertStructureList(param) {
  return request({
    url: 'rest/channel/structureAlertList',
    method: 'post',
    data: param
  })
}

// 查询报警
export function getChannelAlertList(param) {
  return request({
    url: 'rest/channel/channelAlertList',
    method: 'post',
    data: param
  })
}

