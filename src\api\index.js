import request from '@/utils/request'

export const confirmEmergencyApi = {
  list: params =>
    request({
      url: '/rest/confirmEmergency/list',
      method: 'post',
      data: params
    }),
  get: params =>
    request({
      url: '/rest/confirmEmergency/get',
      method: 'post',
      data: params
    }),
  confirm: params =>
    request({
      url: '/rest/confirmEmergency/confirm',
      method: 'post',
      data: params
    })
}

export const docApi = {
  attachList: params =>
    request({
      url: '/rest/doc/attachList',
      method: 'post',
      data: params
    })
}

export const userApi = {
  list: params =>
    request({
      url: '/rest/user/list',
      method: 'post',
      data: params
    })
}
