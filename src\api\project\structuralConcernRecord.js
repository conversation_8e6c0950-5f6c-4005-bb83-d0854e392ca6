import request from '@/utils/request'

// 结构关注检查记录
const structuralConcernsRecordProvider =
  {
    list: (param) => request({
      url: 'rest/structuralConcernsRecord/list',
      method: 'post',
      data: param
    }),
    // 按报表类型
    save: (param) => request({
      url: 'rest/structuralConcernsRecord/save',
      method: 'post',
      data: param
    }),
    // 按报表类型
    get: (param) => request({
      url: 'rest/structuralConcernsRecord/get',
      method: 'post',
      data: param
    }),
    // 按报表类型
    delete: (param) => request({
      url: 'rest/structuralConcernsRecord/delete',
      method: 'post',
      data: param
    })
  }
export default structuralConcernsRecordProvider
