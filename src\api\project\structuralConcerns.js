import request from '@/utils/request'

// 结构关注点
const structuralConcernsProvider =
  {
    list: (param) => request({
      url: 'rest/structuralConcerns/list',
      method: 'post',
      data: param
    }),
    save: (param) => request({
      url: 'rest/structuralConcerns/save',
      method: 'post',
      data: param
    }),
    get: (param) => request({
      url: 'rest/structuralConcerns/get',
      method: 'post',
      data: param
    }),
    delete: (param) => request({
      url: 'rest/structuralConcerns/delete',
      method: 'post',
      data: param
    })
  }
export default structuralConcernsProvider
