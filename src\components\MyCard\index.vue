<template>
  <div class="MyCard">
    <el-card class="box-card mxCard">
      <div class="table" v-if="title">
        <span class="titleSpan" v-bind:style="{marginLeft:spanLeft}">{{ title }}</span>
        <img class="titleImg" src="../../assets/title.png">
      </div>
      <slot/>
    </el-card>
  </div>
</template>

<script>
  export default {
    name: 'MyCard',
    props: {
      title: { type: String, default: '' },
      spanLeft: { type: String, default: '' }
    },
    data() {
      return {}
    },
    mounted() {
      document.getElementsByClassName('mxCard')[0].style.minHeight = document.getElementById('app').clientHeight - 170 + 'px'
    },
    methods: {}
  }
</script>

<style rel="stylesheet/scss" lang="scss">
  @import "../../styles/mixin";

  .mxCard {
    border-top: 5px solid #05C1FA;
    .titleSpan {
      @include tranformTitleSpan(-18px, 0px);
    }

    .titleImg {
      @include tranformTitleImg(-46px, -33px);
    }

    .filter-container {
      margin-left: 80px !important;
    }
  }
</style>
