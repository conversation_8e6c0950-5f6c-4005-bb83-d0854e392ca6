<!-- description -->
<template>
  <div class="dp-pannel-side" :class="[className, align]">
    <div class="pannel-body">
      <slot/>
    </div>
    <div class="hide-button" :class="align" v-if="!alwaysShow">
      <img src="../../assets/screen/side-arrow.png" @click.stop="togglePannel">
    </div>
  </div>
</template>

<script>
  export default {
    name: 'PannelSide',
    components: {},
    directives: {},
    props: {
      alwaysShow: {
        type: Boolean,
        default: false
      },
      align: {
        type: String,
        default: 'right'
      }
    },
    data() {
      return {
        isOpen: true
      }
    },
    computed: {
      className() {
        return this.isOpen ? 'open' : 'close'
      }
    },
    created() {},
    mounted() {},
    methods: {
      togglePannel() {
        this.isOpen = !this.isOpen
      }
    }
  }
</script>

<style rel="stylesheet/scss" lang="scss">
.dp-pannel-side {
  width: fit-content;
  margin: 0;
  border: 1px solid white;
  border-left: none;
  border-radius: 0 .75rem .75rem 0;
  backdrop-filter: blur(.75rem);
  background: linear-gradient(103.44deg,
    rgba(158, 177, 188, 0.3) 9.65%,
    rgba(159, 180, 191, 0.255) 33.19%,
    rgba(216, 237, 247, 0.18) 66.82%,
    rgba(255, 255, 255, 0.18) 85.32%);
  &.right {
    border: 1px solid white;
    border-right: none;
    border-radius: .75rem 0 0 .75rem ;
  }
  .hide-button {
    width: 1.875rem;
    height: 8.125rem;
    position: absolute;
    background: url("../../assets/screen/side-btn-bg.png");
    transform: rotate(180deg);
    display: flex;
    justify-content: center;
    align-items: center;
    img {
      transform: rotate(180deg);
      cursor: pointer;
    }
    &.left {
      right: -1.875rem;
      top: 3rem;
    }
    &.right {
      left: -1.875rem;
      top: 3rem;
      transform: rotate(0);
    }
  }
  .pannel-body {
    width: fit-content;
  }
  &.close {
    border: none;
    .pannel-body {
      width: 0;
      overflow: hidden;
      transition: all .2s;
    }
  }
}
</style>
