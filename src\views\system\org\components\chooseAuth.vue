<template>
  <div class="chooseAuthDiv">
    <el-tree
      :data="formatData"
      show-checkbox
      ref="tree"
      node-key="id" :default-expand-all="true"
      :default-checked-keys="orgPrivilges"
      :props="defaultProps"
      @check-change="handleCheckChange">
    </el-tree>
  </div>
</template>

<script>
  export default {
    name: 'Choose<PERSON><PERSON>',
    props: {
      data: {
        type: Array,
        default: () => []
      },
      orgPrivilges: {
        type: Array,
        default: () => []
      }
    },
    data() {
      return {
        defaultProps: {
          children: 'children',
          label: 'name'
        }
      }
    },
    computed: {
      formatData() {
        let tmp
        if (this.data.length > 0) {
          // 组装参数
          tmp = this.treeListUtil(this.data)
        } else {
          tmp = this.data
        }
        return tmp
      }
    },
    watch: {
      orgPrivilges: function(val) {
        this.$refs.tree.setCheckedKeys(val)
        this.$store.dispatch('setCheckAuth', val)
      }
    },
    mounted() {
    },
    methods: {
      treeListUtil(data, parentId) {
        const itemArr = []
        for (let i = 0; i < data.length; i++) {
          const node = data[i]
          if (node.parentId === parentId) {
            node.children = this.treeListUtil(data, node.id)
            itemArr.push(node)
          }
        }
        return itemArr
      },
      handleCheckChange() {
        // 传入store
        this.$store.dispatch('setCheckAuth', [].concat(this.$refs.tree.getCheckedKeys(), this.$refs.tree.getHalfCheckedKeys()))
      }
    }
  }
</script>

<style rel="stylesheet/scss" lang="scss">
</style>
