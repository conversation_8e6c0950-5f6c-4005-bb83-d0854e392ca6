import request from '@/utils/request'

export function structureList(param) {
  return request({
    url: 'rest/structure/list',
    method: 'post',
    data: param
  })
}

export function structureListPage(param) {
  return request({
    url: 'rest/structure/listPage',
    method: 'post',
    data: param
  })
}

export function getStructureList(param) {
  return request({
    url: 'rest/structure/structureList',
    method: 'post',
    data: param
  })
}

