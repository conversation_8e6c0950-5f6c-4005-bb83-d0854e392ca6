<!--选择经纬度-->
<template>
  <div class="mapDiv">
    <el-button class="filter-item" size="mini" type="primary" @click="visible = true">{{ displayText }}</el-button>
    <el-dialog title="选取坐标" :visible.sync="visible" append-to-body>
      <el-form ref="positionForm" :model="positionForm" label-position="center" size="small"
               label-width="100px">
        <el-row>
          <el-col :span="16">
            <el-form-item label="关键字" prop="detailAddress">
              <el-input type="text" class="filter-item" placeholder="请输入关键字" id="keyInput" v-model="positionForm.name">
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <div style=" height: 300px;" id="container"></div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" @click="surePosition">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
  // import AMap from 'AMap'

  export default {
    name: 'CoordinateSelect',
    props: {
      location: {
        type: Object
      },
      isChoose: { type: Boolean, default: false }
    },
    data() {
      return {
        map: null,
        marker: null,
        visible: false,
        displayText: '未选取',
        positionForm: {
          name: '',
          longitude: '',
          latitude: ''
        }
      }
    },
    watch: {
      visible(val) {
        if (val && !this.map) {
          this.$nextTick(() => {
            this.init()
          })
        }
      },
      isChoose(val) {
        this.displayText = val ? '已选取' : '未选取'
      },
      location(val) {
        const proto = Object.getPrototypeOf(val)
        this.positionForm = Object.assign({}, Object.create(proto), val)
        this.$nextTick(() => {
          this.init()
        })
      }
    },
    mounted() {
      this.displayText = this.isChoose ? '已选取' : '未选取'
      const proto = Object.getPrototypeOf(this.location)
      this.positionForm = Object.assign({}, Object.create(proto), this.location)
    },
    methods: {
      init: function() {
        const me = this
        if (me.positionForm.longitude && me.positionForm.longitude !== '') {
          this.map = new AMap.Map('container', {
            resizeEnable: true,
            center: [me.positionForm.longitude, me.positionForm.latitude],
            zoom: 12
          })
        } else {
          this.map = new AMap.Map('container', {
            resizeEnable: true,
            zoom: 5
          })
        }
        this.map.on('complete', function() {
          if (me.positionForm.longitude && me.positionForm.longitude !== '') {
            me.addMarker([me.positionForm.longitude, me.positionForm.latitude])
          } else {
            me.addMarker(me.map.getCenter())
          }
        })
        const autoOptions = {
          input: 'keyInput'
        }
        const auto = new AMap.Autocomplete(autoOptions)
        AMap.event.addListener(auto, 'select', function(e) {
          me.selectText(e)
        })
        // AMap.plugin(['AMap.ToolBar', 'AMap.Scale'], function() {
        //   me.map.addControl(new AMap.ToolBar())
        //   me.map.addControl(new AMap.Scale())
        // })
      },
      selectText(e) {
        this.map.setZoomAndCenter(14, [e.poi.location.lng, e.poi.location.lat])
        this.addMarker([e.poi.location.lng, e.poi.location.lat])
      },
      // 添加覆盖物
      addMarker(position) {
        if (this.marker) {
          this.map.remove(this.marker)
          this.marker = null
        }
        this.marker = new AMap.Marker({
          position: position,
          draggable: true,
          cursor: 'move',
          raiseOnDrag: true
        })
        this.marker.setMap(this.map)
      },
      surePosition() {
        this.positionForm.longitude = this.marker.getPosition().lng
        this.positionForm.latitude = this.marker.getPosition().lat
        this.$emit('getLocation', this.positionForm)
        this.displayText = '已选取'
        this.positionForm.name = ''
        this.visible = false
      }
    }
  }
</script>

<style rel="stylesheet/scss" lang="scss">
</style>
