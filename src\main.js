import Vue from 'vue'
import 'normalize.css/normalize.css'
import Element from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import highcharts from 'highcharts'
import exporting from 'highcharts/modules/exporting'
import * as echarts from 'echarts/core'
import '@/styles/index.scss' // 全局CSS

import App from './App'
import router from './router'
import store from './store'

import i18n from './lang' // 多语言
import './errorLog'// 错误日志
import './permission' // 权限控制
import * as filters from './filters' // 过滤器
import myStroage from './utils/sessionStorage' // 本地session存储
import axios from 'axios' // 以后外部资源调用
import 'echarts/theme/macarons.js'

import waves from '@/directive/waves'


exporting(highcharts) // exporting插件
let configPath = './static/config.json'
if (process.env.NODE_ENV === 'development') {
  configPath = '../static/config.json'
}

axios.get(configPath).then(response => {
  Vue.use(waves)
  Vue.prototype.panoRotateSpeed = response.data.pano.rotateSpeed
  if (process.env.NODE_ENV === 'production') {
    Vue.prototype.myUrl = response.data.host.prodHost
  } else {
    Vue.prototype.myUrl = response.data.host.devHost
  }

  Vue.prototype.demoUrl = response.data.host.demoHost
  Vue.use(Element, {
    size: 'medium',
    i18n: (key, value) => i18n.t(key, value)
  })

  // 注册全部过滤器
  Object.keys(filters).forEach(key => {
    Vue.filter(key, filters[key])
  })
  Vue.prototype.$storage = myStroage // session存储全局化
  Vue.prototype.$highChart = highcharts
  Vue.prototype.$echarts = echarts
  Vue.config.productionTip = false
  // 初始化字典信息
  store.dispatch('setFileUrl')
  store.dispatch('setDictMap')
  // store.dispatch('setMessageNum')
  new Vue({
    el: '#app',
    router,
    store,
    i18n,
    render: h => h(App)
  })
})

