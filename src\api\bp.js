import request from '@/utils/request'

export function projectStatistics(param) {
  return request({
    url: 'rest/bp/projectStatistics',
    method: 'post',
    data: param
  })
}

export function alertStatistics(param) {
  return request({
    url: 'rest/bp/alertStatistics',
    method: 'post',
    data: param
  })
}

export function carFlow(param) {
  return request({
    url: 'rest/bp/carFlow',
    method: 'post',
    data: param
  })
}

export function carSpeed(param) {
  return request({
    url: 'rest/bp/carSpeed',
    method: 'post',
    data: param
  })
}

export function getDeviceLegendList(param) {
  return request({
    url: 'rest/bp/deviceTypeList',
    method: 'post',
    data: param
  })
}
// 报警分析接口
export function alarmAnalysis(param) {
  return request({
    url: 'rest/bp/alarmAnalysis',
    method: 'post',
    data: param
  })
}

// 多项目首页查询
export function bpProjectList(param) {
  return request({
    url: 'rest/bp/projectList',
    method: 'post',
    data: param
  })
}

/* 单项目大屏设备查询 state 0 1一级报警 2二级报警
  deviceTypeIds projectId */
export function bpDeviceList(param) {
  return request({
    url: 'rest/bp/deviceList',
    method: 'post',
    data: param
  })
}
