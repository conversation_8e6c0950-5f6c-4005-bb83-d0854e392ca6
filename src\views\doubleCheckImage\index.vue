<!-- 事件二次确认 -->
<template>
  <div class="app-container double-check-container">
    <my-card title="事件二次确认">
      <div class="filter-container" style="display: none;">
        <el-date-picker
          class="filter-item dateRange"
          style="display: inline-flex"
          v-model="chooseDate"
          type="datetimerange"
          range-separator="—-"
          value-format="yyyy-MM-dd HH:mm:ss"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        >
        </el-date-picker>
        <SelectEventType
          v-model="eventType"
          class="filter-item"
          :width="340"
          placeholder="事件类型"
          @selectChange="handeleEventClick"
        ></SelectEventType>
        <el-select
          class="filter-item"
          style="width: 200px"
          clearable
          v-model="listQuery.params.checkResult"
          placeholder="是否属实"
          @change="search"
        >
          <el-option label="属实" :value="1"></el-option>
          <el-option label="误报" :value="-1"></el-option>
        </el-select>
        <el-select
          class="filter-item"
          style="width: 200px"
          clearable
          v-model="listQuery.params.process"
          placeholder="请选择是否处理"
          @change="search"
        >
          <el-option label="未处理" :value="0"></el-option>
          <el-option label="已处理" :value="1"></el-option>
        </el-select>
        <el-select
          class="filter-item"
          style="width: 200px"
          clearable
          v-model="listQuery.params.userId"
          placeholder="请选择审核人员"
          @change="search"
        >
          <el-option label="未处理" :value="0"></el-option>
          <el-option label="已处理" :value="1"></el-option>
        </el-select>

        <div style="float: right">
          <el-button
            class="filter-item"
            type="primary"
            v-waves
            icon="el-icon-search"
            @click="search"
          >查询</el-button
          >
          <el-button
            style="margin-left: 30px"
            class="filter-item"
            type="primary"
            v-waves
            icon="el-icon-refresh"
            @click="resetListQuery"
          >重置</el-button
          >
        </div>
      </div>
      <el-row :gutter="20">
        <el-col :span="8" v-for="item in dataList" :key="item.id">
          <el-card style="text-align: center;">
            <div
              style="width: 100%; height: 30px; display: flex; justify-content: space-between;"
            >
              <div style="color:red;line-height: 30px;">
                {{ item.dllptSubType }}
              </div>
              <div style="color:red;line-height: 30px;">
                {{ parseTime(item.alertTime) }}
              </div>
            </div>
            <img
              :src="item.imageUrl"
              class="image"
              style="width:500px; cursor: zoom-in;"
              @click="showImg(item)"
            />
            <div style="padding: 14px;">
              <div class="bottom clearfix">
                <el-radio-group v-model="item.doubleCheckResult">
                  <el-radio :label="1">通过</el-radio>
                  <el-radio :label="-1">不通过</el-radio>
                </el-radio-group>
                <el-button type="primary" size="mini" @click="submitCheck(item)"
                >提交</el-button
                >
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <div
        style="width: 100%;height: 50px; display: flex; justify-content: center;align-items: center;"
      >
        <el-pagination
          background
          layout="prev, pager, next"
          :total="total"
          @current-change="currentChange"
        ></el-pagination>
      </div>
    </my-card>
    <el-image-viewer
      v-if="showViewer"
      :url-list="urlList"
      :on-close="closeViewer"
    ></el-image-viewer>
  </div>
</template>

<script>
import MyCard from '@/components/MyCard/index.vue'
import SelectDict from '@/components/SelectDict/index.vue'
import waves from '@/directive/waves'
import { confirmEmergencyApi } from '@/api/index.js'
import SelectEventType from '@/components/SelectEventType/index.vue'
import ElImageViewer from '@/components/image-viewer.vue'
import { parseTime } from '@/utils'

export default {
  name: 'DoubleCheckImage',
  components: {
    MyCard,
    SelectDict,
    SelectEventType,
    ElImageViewer
  },
  directives: { waves },
  data() {
    return {
      showViewer: false,
      dataList: [],
      listLoading: false,
      chooseDate: [],
      eventType: null,
      listQuery: {
        pageSize: 30,
        currPage: 1,
        sortRule: 'alertTime:desc',
        params: {
          startTime: '',
          endTime: '',
          category: null,
          type: null,
          subType: null,
          checkResult: null,
          process: 0,
          userId: null
        }
      },
      listQueryOriginal: {},
      total: 0,
      pointList: [], // 识别点位下拉框数据
      selectList: [
        {
          id: '1',
          name: '属实'
        },
        {
          id: '0',
          name: '误报'
        }
      ],
      dealList: [
        {
          id: '0',
          name: '进行中'
        },
        {
          id: '2',
          name: '已结束'
        }
      ],
      selectData: [
        {
          id: '1',
          name: '是'
        },
        {
          id: '0',
          name: '否'
        }
      ],
      IsEdit: false, // 单据操作状态：false--查看 true--编辑
      tableId: 1,
      urlList: [],
      userList: []
    }
  },
  computed: {},
  watch: {
    chooseDate(val) {
      if (val) {
        this.listQuery.params.startTime = this.chooseDate[0]
        this.listQuery.params.endTime = this.chooseDate[1]
        this.search()
      } else {
        this.listQuery.params.startTime = null
        this.listQuery.params.endTime = null
        this.search()
      }
    }
  },
  mounted() {
    this.listQueryOriginal = JSON.parse(JSON.stringify(this.listQuery))
    this.getList()

    setInterval(() => {
      this.getList()
    }, 60000)
  },
  methods: {
    handeleEventClick(val) {
      if (val) {
        this.listQuery.params = {
          category: val.category,
          type: val.type,
          subType: val.subType
        }
      } else {
        this.listQuery.params = {
          category: null,
          type: null,
          subType: null
        }
      }

      this.search()
    },
    // 获取所有列表
    getList() {
      confirmEmergencyApi.list(this.listQuery).then(res => {
        console.log('列表', res)
        if (res.success) {
          this.dataList = res.result.list
          this.total = res.result.total
        }
      })
    },
    search() {
      this.listQuery.currPage = 1
      this.getList()
    },
    resetListQuery() {
      this.listQuery = JSON.parse(JSON.stringify(this.listQueryOriginal))
      this.search()
    },
    handleSizeChange(val) {
      this.listQuery.pageSize = val
      this.getList()
    },
    currentChange(val) {
      this.listQuery.currPage = val
      this.getList()
    },
    // 表格操作按鈕
    operButton(val) {
      if (
        val.checkResult === undefined ||
        val.checkResult === null ||
        val.checkResult === ''
      ) {
        return [{ class: 'icon-z', value: '处理', click: this.dealEvent }]
      }
      return [{ class: 'icon-z', value: '查看', click: this.viewOne }]
    },
    viewOne(val) {
      console.log(val, 'res.val')
      this.IsEdit = false
      this.tableId = val.row.id
      this.$refs.eventForm.openDialog(val.row.id)
    },
    dealEvent(val) {
      this.IsEdit = true
      this.tableId = val.row.id
      this.$refs.eventForm.openDialog(val.row.id)
    },
    handleSaveSuccess() {
      this.getList()
    },
    closeViewer() {
      this.showViewer = false
    },
    showImg(item) {
      this.urlList = [item.imageUrl]
      this.showViewer = true
    },
    submitCheck(item) {
      console.log('提交', item)
      confirmEmergencyApi
        .confirm({ id: item.id, check_result: item.doubleCheckResult })
        .then(res => {
          if (res.success) {
            console.log(res.result, 'res.result-confirm')
            this.getList()
          }
        })
    },
    parseTime

  }
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.double-check-container {
  /deep/.mxCard .titleSpan {
    margin-left: -30px;
  }
  .filter-container {
    margin-left: 40px;
  }
  .label-name {
    font-size: 14px;
    position: relative;
    top: -5px;
  }
  .vue-treeselect {
    height: 32px;
  }
  .imgCol {
    img {
      width: 148px;
      height: 148px;
      margin-right: 10px;
    }
  }
  .radio-group {
    margin-left: 50px;
    width: 350px;
    display: flex;
    justify-content: center;
    text-align: center;
    flex-direction: column;
  }
}
.btnDiv {
  margin-top: 20px;
  text-align: center;
}
</style>
<style>
.green {
  color: green;
}
.red {
  color: #ff0000;
}
</style>
