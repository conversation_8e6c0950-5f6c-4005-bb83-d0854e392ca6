import Vue from 'vue'
import Router from 'vue-router'

const _import = require('./_import_' + process.env.NODE_ENV)
// in development env not use Lazy Loading,because Lazy Loading too many pages will cause webpack hot update too slow.so only in production use Lazy Loading

/* layout */
import Layout from '../views/layout/Layout'

Vue.use(Router)

/**
 * icon : 菜单栏图片
 * hidden : 是否显示
 * meta : `{ privileges: ['P_zzgl'] }` 定义权限
 **/
export const constantRouterMap = [
  { path: '/login', component: _import('login/index'), hidden: true },
  {
    path: '/authredirect',
    component: _import('login/authredirect'),
    hidden: true
  },
  { path: '/404', component: _import('errorPage/404'), hidden: true },
  { path: '/401', component: _import('errorPage/401'), hidden: true },

  {
    path: '',
    component: Layout,
    redirect: 'dashboard',
    hidden: true,
    children: [
      {
        path: 'dashboard',
        component: _import('dashboard/index'),
        name: 'dashboard',
        meta: { title: '主页', icon: 'iconzhuye', noCache: true }
      }
    ]
  }
]

export default new Router({
  // mode: 'history', //后端支持可开
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRouterMap
})

export const asyncRouterMap = [
  {
    path: '',
    component: Layout,
    redirect: 'dashboard',
    children: [
      {
        path: 'dashboard',
        component: _import('dashboard/index'),
        name: 'dashboard',
        meta: {
          title: '主页',
          icon: 'iconzhuye',
          noCache: true,
          privileges: ['P_zy']
        }
      }
    ]
  },
  {
    path: 'external-link',
    component: Layout,
    children: [
      {
        path: 'bigscreen',
        meta: { title: '大屏展示', icon: 'link' }
      }
    ]
  },
  {
    path: '',
    component: Layout,
    redirect: 'DoubleCheckImage',
    children: [
      {
        path: 'doubleCheckImage',
        component: _import('doubleCheckImage/index'),
        name: 'DoubleCheckImage',
        meta: {
          title: '事件二次确认工作台',
          // icon: "iconzhuye",
          noCache: true
          // privileges: ['P_zy']
        }
      }
    ]
  },
  {
    path: '',
    component: Layout,
    redirect: 'doubleCheck',
    children: [
      {
        path: 'doubleCheck',
        component: _import('doubleCheck/index'),
        name: 'doubleCheck',
        meta: {
          title: '事件二次确认列表',
          // icon: "iconzhuye",
          noCache: true
          // privileges: ['P_zy']
        }
      }
    ]
  },
  // {
  //   path: '/project',
  //   component: Layout,
  //   redirect: 'projectList',
  //   name: 'project',
  //   meta: {
  //     title: '项目管理',
  //     icon: 'iconxiangmu',
  //     privileges: ['P_xmgl']
  //   },
  //   children: [
  //     {
  //       path: 'projectList',
  //       component: _import('project/projectList'),
  //       name: 'projectList',
  //       meta: {
  //         title: '项目管理',
  //         icon: 'iconccgl-xiangmuguanli-3',
  //         noCache: true,
  //         privileges: ['P_xmlb']
  //       }
  //     }
  //   ]
  // },
  // {
  //   path: '/standard',
  //   component: Layout,
  //   redirect: 'dictionary',
  //   name: 'standard',
  //   meta: {
  //     title: '标准管理',
  //     icon: 'iconic_configchecklist',
  //     privileges: ['P_bzgl']
  //   },
  //   children: [
  //     {
  //       path: 'deviceTypeManage',
  //       component: _import('system/deviceTypeManage/index'),
  //       name: 'deviceTypeManage',
  //       meta: {
  //         title: '监测项目',
  //         icon: 'iconshebeileixingguanli',
  //         noCache: true,
  //         privileges: ['P_sblx']
  //       }
  //     },
  //     {
  //       path: 'diseaseType',
  //       component: _import('system/diseaseType/index'),
  //       name: 'DiseaseType',
  //       meta: {
  //         title: '缺陷分类',
  //         icon: 'iconjichushijianfenlei',
  //         noCache: false,
  //         privileges: ['P_qxlx']
  //       }
  //     }
  //   ]
  // },
  {
    path: '/system',
    component: Layout,
    redirect: 'dictionary',
    name: 'system',
    meta: { title: '系统管理', icon: 'iconxitong' },
    children: [
      {
        path: 'dictionary',
        component: _import('system/dictionary/index'),
        name: 'dictionary',
        meta: {
          title: '字典管理',
          icon: 'iconzidian',
          noCache: true,
          privileges: ['p_zdgl']
        }
      },
      // {
      //   path: 'systemPara',
      //   component: _import('system/parameter/index'),
      //   name: 'SystemPara',
      //   meta: {
      //     title: '系统参数',
      //     icon: 'iconzidian',
      //     noCache: true,
      //     privileges: ['P_xtcspz']
      //   }
      // },
      {
        path: 'authority',
        component: _import('system/authority/index'),
        name: 'authority',
        meta: {
          title: '权限管理',
          icon: 'iconquanxianguanli',
          noCache: true,
          privileges: ['P_qxgl']
        }
      },
      {
        path: 'org',
        component: _import('system/org/index'),
        name: 'org',
        meta: {
          title: '组织管理',
          icon: 'iconzuzhi',
          noCache: true,
          privileges: ['P_zzgl']
        }
      },
      {
        path: 'role',
        component: _import('system/role/index'),
        name: 'role',
        meta: {
          title: '角色管理',
          icon: 'iconjiaoseguanli',
          noCache: true,
          privileges: ['P_jsgl']
        }
      },
      {
        path: 'user',
        component: _import('system/user/index'),
        name: 'user',
        meta: {
          title: '用户管理',
          icon: 'iconyonghu',
          noCache: true,
          privileges: ['P_yhgl']
        }
      },
      {
        path: 'log',
        component: _import('system/log/index'),
        name: 'log',
        meta: {
          title: '系统日志',
          icon: 'iconrizhi',
          noCache: true,
          privileges: ['P_xtrz']
        }
      }
      // {
      //   path: 'message',
      //   component: _import('system/message/index'),
      //   name: 'message',
      //   meta: {
      //     title: '消息管理',
      //     icon: 'iconshebeileixingguanli',
      //     noCache: true,
      //     privileges: ['p_xxgl']
      //   }
      // },
      // {
      //   path: 'BIMViewer',
      //   component: _import('system/BIMViewer/index'),
      //   name: 'BIMViewer',
      //   meta: {
      //     title: '模型视口',
      //     icon: 'iconmoxingliulan',
      //     noCache: true,
      //     privileges: ['p_mxsk']
      //   }
      // }
    ]
  },
  { path: '*', redirect: '/404', hidden: true }
]
