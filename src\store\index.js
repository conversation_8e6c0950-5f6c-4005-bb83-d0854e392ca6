import Vue from 'vue'
import Vuex from 'vuex'
import app from './modules/app'
import errorLog from './modules/errorLog'
import permission from './modules/permission'
import tagsView from './modules/tagsView'
import user from './modules/user'
import dictionary from './modules/dictionary'
import auth from './modules/auth'
import project from './modules/project'
import getters from './getters'
import views from './modules/views'
import screen from './modules/screen'
import alert from './modules/alert'

Vue.use(Vuex)

const store = new Vuex.Store({
  modules: {
    app,
    errorLog,
    permission,
    tagsView,
    user,
    dictionary,
    auth,
    project,
    views,
    screen,
    alert
  },
  getters
})

export default store
