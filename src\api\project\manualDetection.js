import request from '@/utils/request'

export function getManualDetectionList(param) {
  return request({
    url: 'rest/manualDetection/list',
    method: 'post',
    data: param
  })
}

export function deleteManualDetection(param) {
  return request({
    url: 'rest/manualDetection/delete',
    method: 'post',
    data: param
  })
}

export function saveManualDetection(param) {
  return request({
    url: 'rest/manualDetection/save',
    method: 'post',
    data: param
  })
}

export function getManualDetection(param) {
  return request({
    url: 'rest/manualDetection/get',
    method: 'post',
    data: param
  })
}

export function getDataAndDevice(param) {
  return request({
    url: 'rest/data/getDataAndDevice',
    method: 'post',
    data: param
  })
}

