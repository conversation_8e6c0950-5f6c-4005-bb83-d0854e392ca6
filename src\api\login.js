import request from '@/utils/request'

export function loginByUsername(data) {
  return request({
    url: 'login',
    method: 'post',
    data: data
  })
}

export function logout() {
  return request({
    url: 'rest/auth/logout',
    method: 'post'
  })
}

export function getUserInfo() {
  return request({
    url: 'rest/auth/getUserInfo',
    method: 'post'
  })
}

// 获取公钥
export function getPub(data) {
  return request({
    url: 'rest/auth/getPub',
    method: 'post',
    data: data
  })
}

