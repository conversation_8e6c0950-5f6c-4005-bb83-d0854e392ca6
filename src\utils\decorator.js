export const validLoading = (ref, loadingName) => {
  return (target, name, descriptor) => {
    const timeout = 10000
    const oldValue = descriptor.value
    descriptor.value = function fn(...args) {
      this.$refs[ref].validate((valid) => {
        if (valid) {
          this[loadingName] = true
          // 需要监听如果超时 将再次置为false
          let timer = setTimeout(() => {
            this[loadingName] = false
            this.$message({
              type: 'warning',
              message: '请求超时'
            })
          }, timeout)
          oldValue.apply(this, args).then(res => {
            this[loadingName] = false
            clearTimeout(timer)
            timer = null
          })
        }
      })
    }
    return descriptor
  }
}

export const onResize = (target, name, descriptor) => {
  const oldValue = descriptor.value
  descriptor.value = function fn(...args) {
    oldValue.apply(this, args)
    // window.onresize 会导致覆盖
    // window.onresize = () => {
    //   oldValue.apply(this, args)
    // }
    window.addEventListener('resize', () => {
      oldValue.apply(this, args)
    })
  }
  return descriptor
}
