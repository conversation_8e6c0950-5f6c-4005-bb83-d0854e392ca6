<!--权限管理-->
<template>
  <div class="app-container authorityDiv">
    <my-card title="权限管理">
      <el-button class="filter-item addButton" type="primary" @click="addAuth" v-waves icon="el-icon-circle-plus-outline">添加</el-button>
      <tree-table :data="data" :columns="columns" :list-loading="listLoading" border></tree-table>
    </my-card>
    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible">
      <el-form :rules="rules" ref="authForm" :model="authForm" label-position="center" size="small" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="权限名称" prop="name">
              <el-input type="text" class="filter-item" placeholder="请输入权限名称" v-model="authForm.name" maxlength="20">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="权限编码" prop="code">
              <el-input type="text" class="filter-item" placeholder="请输入权限编码" v-model="authForm.code" maxlength="20">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="权限定义" prop="definition">
              <el-input type="text" class="filter-item" placeholder="请输入权限定义" v-model="authForm.definition" maxlength="20">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="权限类型" prop="type">
              <el-select v-model="authForm.type" placeholder="请选择权限类型">
                <el-option
                  v-for="item in dictMap['privilege_type']"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="权限范围" prop="scope">
              <el-select v-model="authForm.scope" placeholder="请选择权限范围">
                <el-option
                  v-for="item in dictMap['privilege_scope']"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="权限描述" prop="description">
              <el-input type="text" class="filter-item" placeholder="请输入权限描述" v-model="authForm.description" maxlength="20">
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="saveAuth" :loading="loading" v-waves>提交</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import TreeTable from '@/components/TreeTable'
  import waves from '@/directive/waves'
  import { getList, deleteAuth, saveAuth } from '@/api/system/authority'
  import { mapGetters } from 'vuex'
  import MyCard from '@/components/MyCard'
  import { getNameByCode } from '@/utils'
  export default {
    name: 'Authority',
    components: {
      TreeTable, MyCard
    },
    directives: {
      waves
    },
    data() {
      return {
        dialogFormVisible: false, // 弹出框显示判断
        dialogStatus: 'create',
        textMap: {
          update: '编辑',
          create: '新建'
        },
        loading: false, // 按钮重复提交判断
        listLoading: false,
        rules: {
          name: [{ required: true, message: '请填写权限名称' }],
          code: [{ required: true, message: '请填写权限编码' }],
          type: [{ required: true, message: '请选择权限类型', trigger: 'change' }]
        },
        authForm: {
          id: '',
          parentId: '',
          name: '',
          code: '',
          type: '',
          description: '',
          definition: '',
          scope: ''
        },
        columns: [
          {
            text: '权限名称',
            value: 'name'
          },
          {
            text: '权限编码',
            value: 'code',
            width: 200
          },
          {
            text: '类型',
            value: 'type',
            filter: getNameByCode,
            filterParams: ['privilege_type']
          },
          {
            text: '权限范围',
            value: 'scope',
            filter: getNameByCode,
            filterParams: ['privilege_scope']
          },
          {
            text: '描述',
            value: 'description'
          },
          {
            text: '定义',
            value: 'definition'
          },
          {
            text: '操作',
            type: 'iconButton',
            width: 130,
            list: this.operButton
          }
        ],
        data: []
      }
    },
    computed: {
      ...mapGetters([
        'dictMap'
      ])
    },
    mounted() {
      this.getList()
    },
    methods: {
      // 获取所有权限
      getList() {
        this.listLoading = true
        getList({}).then(response => {
          if (response.success) {
            // this.data = this.treeListUtil(response.result)
            this.data = response.result
          }
          this.listLoading = false
        })
      },
      typeFormatter(val) {
        return val.type === 'menu' ? '菜单' : '按钮'
      },
      // 表格操作按鈕
      operButton(val) {
        if (val.children && val.children.length > 0) {
          return [
            { class: 'iconxinzeng', value: '添加', click: this.addChild },
            { class: 'iconxiugai', value: '编辑', click: this.updateAuth }
          ]
        } else {
          return [
            { class: 'iconxinzeng', value: '添加', click: this.addChild },
            { class: 'iconxiugai', value: '编辑', click: this.updateAuth }, {
              class: 'iconshanchu1', value: '删除', click: this.deleteAuth }]
        }
      },
      // 更新权限
      updateAuth(val) {
        this.dialogStatus = 'update'
        this.authForm.id = val.row.id
        this.authForm.parentId = val.row.parentId
        this.authForm.name = val.row.name
        this.authForm.code = val.row.code
        this.authForm.type = val.row.type
        this.authForm.scope = val.row.scope
        this.authForm.description = val.row.description
        this.authForm.definition = val.row.definition
        this.dialogFormVisible = true
      },
      // 添加子节点
      addChild(val) {
        this.authForm = {
          id: '',
          parentId: val.row.id,
          name: '',
          code: '',
          type: '',
          scope: '',
          description: '',
          definition: ''
        }
        this.dialogFormVisible = true
      },
      // 删除权限
      deleteAuth(val) {
        this.$confirm('是否确定删除该记录?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          deleteAuth({ id: val.row.id }).then(response => {
            if (response.success) {
              this.data.splice(this.data.findIndex(item => item.id === val.row.id), 1)
              this.$message.success('删除成功')
            } else {
              this.$message.error(response.message)
            }
          })
        })
      },
      reset() { // 清空
        this.authForm.id = ''
        this.authForm.parentId = ''
        this.$refs['authForm'].resetFields()
      },
      // 新增权限
      addAuth() {
        this.authForm = {
          id: '',
          parentId: '',
          name: '',
          code: '',
          type: '',
          scope: '',
          description: '',
          definition: ''
        }
        this.dialogStatus = 'create'
        this.dialogFormVisible = true
      },
      setDgData(item, result) {
        item.name = result.name
        item.code = result.code
        item.type = result.type
        item.scope = result.scope
        item.description = result.description
        item.definition = result.definition
      },
      // 保存权限
      saveAuth() {
        this.$refs['authForm'].validate((valid) => {
          if (valid) {
            this.loading = true
            saveAuth(this.authForm).then(response => {
              if (response.success) {
                this.$message.success('保存成功')
                // 操作数结构
                response.result.children = []
                // 判断是更新还是添加
                if (this.authForm.id !== '') {
                  const findVal = this.data.find(item => {
                    return item.id === response.result.id
                  })
                  this.setDgData(findVal, response.result)
                } else {
                  // this.saveUtil(this.authForm.parentId, response.result)
                  this.data.push(response.result)
                }
                this.dialogFormVisible = false
                this.reset()
              } else {
                this.$message.error(response.message)
              }
              this.loading = false
            })
          }
        })
      }
    }
  }
</script>

<style rel="stylesheet/scss" lang="scss">
  .authorityDiv {
    .addButton {
      float: right;
      margin-right: 10px;
      margin-bottom: 10px;
    }
  }

</style>
