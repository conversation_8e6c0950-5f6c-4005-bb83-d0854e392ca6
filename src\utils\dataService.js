import { fetchApiData } from './messageBox'
import { getProperty } from '../api/system/sysProperty'
import { getData } from '../api/channel'
import Vue from 'vue'

let maxLimit = 0
export async function isDataSearchOversize(start, end, period, message = true) {
  const total = new Date(end).getTime() - new Date(start).getTime()
  if (total < 0) {
    return false
  }
  if (!maxLimit) {
    const result = await fetchApiData(getProperty, { code: 'data_search_max_limit' })
    maxLimit = parseInt(result.value)
  }
  // const maxLimit = 100
  const periodLong = {
    D: 24 * 60 * 60 * 1000,
    H: 60 * 60 * 1000,
    M: 60 * 1000,
    S: 1000,
    MS: 1
  }
  let pL = 0
  const list = ['H', 'D', 'MS', 'M', 'S']
  for (let i = 0; i < list.length; i++) {
    if (!pL && period.indexOf(list[i]) !== -1) {
      pL = periodLong[list[i]] * parseInt(period)
    }
  }
  if (!pL) {
    if (message) {
      Vue.prototype.$message.error('采集周期未配置')
    }
    return true
  }
  const num = parseInt(total / pL)
  console.log(`查询 ${num} 条数据`)
  if (num > maxLimit) {
    console.log(`超过最大查询数据 ${maxLimit}`)
    if (message) {
      Vue.prototype.$message.error('当前查询时段过长，请重新选择查询时段')
    }
    return true
  }
  return false
}

export async function getDataUnderLimit(start, end, channel, message = true) {
  const isOversize = await isDataSearchOversize(start, end, channel.period, message)
  if (isOversize) {
    return new Promise(resolve => resolve({ success: false }))
  }
  return getData({
    startDate: start,
    endDate: end,
    channelId: channel.id
  })
}
