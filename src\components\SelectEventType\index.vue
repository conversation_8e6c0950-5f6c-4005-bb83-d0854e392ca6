<template>
  <el-select
    :disabled="disabled"
    :style="{ width: width + 'px' }"
    v-model="code"
    :placeholder="`请选择${placeText}`"
    :multiple="multiple"
    :filterable="filterable"
    :size="size"
    @change="handleChange"
    :clearable="clearable"
  >
    <el-option
      v-for="(item, index) in list"
      :key="index"
      :label="item.name"
      :value="item.id"
    >
    </el-option>
  </el-select>
</template>

<script>
export default {
  name: 'SelectEventType',
  model: {
    prop: 'currentCode',
    event: 'change'
  },
  /**
   * Props 参数
   * @prop {String|Array} currentCode v-model 绑定值
   * @prop {String} placeText placeholder文字
   * @prop {Number} width 宽度
   * @prop {Boolean} disabled 是否禁用
   * @prop {Array} removeItems 移除项
   * @prop {Boolean} multiple 是否多选
   */
  props: {
    currentCode: {
      type: String | Array,
      default: ''
    },
    placeText: {
      type: String,
      default: ''
    },
    width: {
      type: Number | String,
      default: 200
    },
    disabled: {
      type: Boolean,
      default: false
    },
    removeItems: {
      type: Array,
      default: () => []
    },
    codeList: {
      type: Array,
      default: () => []
    },
    multiple: {
      type: Boolean,
      default: false
    },
    clearable: {
      type: Boolean,
      default: true
    },
    filterable: {
      type: Boolean,
      default: false
    },
    size: {
      type: String,
      default: 'medium'
    }
  },
  data() {
    return {
      code: this.currentCode,
      list: [
        { id: 1, name: '单车抛锚', category: 10, type: 40, subType: null },
        { id: 2, name: '道路拥堵', category: 8, type: 36, subType: 22 },
        { id: 3, name: '人员非法驻留', category: 4, type: 42, subType: 4203 },
        { id: 4, name: '道路障碍物', category: 5, type: 33, subType: 12 },
        { id: 5, name: '设备故障', category: 9, type: 37, subType: 105 },
        { id: 6, name: '火灾', category: 4, type: 30, subType: 101 },
        {
          id: 7,
          name: '事故灾难危险品泄露',
          category: 4,
          type: 28,
          subType: null
        }
      ]
    }
  },
  watch: {
    code() {
      this.$emit('change', this.code)
    },
    currentCode() {
      this.code = this.currentCode
      // this.$emit('change', this.code)
    }
  },
  mounted() {},
  methods: {
    handleChange(val) {
      const selectedItem = this.list.find(_ => _.id === val)
      this.$emit('selectChange', selectedItem)
    }
  }
}
</script>
