import request from '@/utils/request'

// 设备类型列表
export function getDeviceTypeList(param) {
  return request({
    url: 'rest/deviceType/list',
    method: 'post',
    data: param
  })
}

// 设备类型删除
export function deleteDeviceType(param) {
  return request({
    url: 'rest/deviceType/delete',
    method: 'post',
    data: param
  })
}

// 设备类型保存
export function saveDeviceType(param) {
  return request({
    url: 'rest/deviceType/save',
    method: 'post',
    data: param
  })
}

export function QueryDeviceType(param) {
  return request({
    url: 'rest/deviceType/get',
    method: 'post',
    data: param
  })
}

// 测点列表
export function getMeasureList(param) {
  return request({
    url: 'rest/deviceType/measureList',
    method: 'post',
    data: param
  })
}

// 测点保存
export function saveMeasure(param) {
  return request({
    url: 'rest/deviceType/saveMeasure',
    method: 'post',
    data: param
  })
}

// 变量保存
export function saveVariable(param) {
  return request({
    url: 'rest/deviceType/saveVariable',
    method: 'post',
    data: param
  })
}

// 测点删除
export function deleteMeasure(param) {
  return request({
    url: 'rest/deviceType/deleteMeasure',
    method: 'post',
    data: param
  })
}

export function getPicture(param) {
  return request({
    url: 'rest/doc/attachList',
    method: 'post',
    data: param
  })
}

export function getSonListByParentId(param) {
  return request({
    url: 'rest/deviceType/getSonListByParentId',
    method: 'post',
    data: param
  })
}

export function getParentOrSonList(param) {
  return request({
    url: 'rest/deviceType/getParentOrSonList',
    method: 'post',
    data: param
  })
}

export function getDeviceType(param) {
  return request({
    url: 'rest/deviceType/getDeviceType',
    method: 'post',
    data: param
  })
}

export function listChildrenAndParent(param) {
  return request({
    url: 'rest/deviceType/listChildrenAndParent',
    method: 'post',
    data: param
  })
}

// 完整性统计相关
export function deviceTypeListByChannel(param) {
  return request({
    url: 'rest/deviceTypeDiy/deviceTypeListByChannel',
    method: 'post',
    data: param
  })
}

