const app = {
  state: {
    moduleId: '',
    projectOrgId: null
  },
  mutations: {
    SET_MODULE_ID: (state, moduleId) => {
      state.moduleId = moduleId
    },
    SET_PROJECT_ORG_ID: (state, projectOrgId) => {
      state.projectOrgId = projectOrgId
    }
  },
  actions: {
    setModuleId({ commit }, moduleId) {
      commit('SET_MODULE_ID', moduleId)
    },
    setProjectOrgId({ commit }, projectOrgId) {
      commit('SET_PROJECT_ORG_ID', projectOrgId)
    }
  }
}

export default app
