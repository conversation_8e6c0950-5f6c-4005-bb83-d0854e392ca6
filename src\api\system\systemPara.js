import request from '@/utils/request'

// 删除单条系统参数表
export function paraDelete(param) {
  return request({
    url: 'rest/sysProperty/delete',
    method: 'post',
    data: param
  })
}

// 获取单条系统参数表
export function getParam(param) {
  return request({
    url: 'rest/sysProperty/get',
    method: 'post',
    data: param
  })
}

// 分页查询系统参数表列表
export function listParam(param) {
  return request({
    url: 'rest/sysProperty/list',
    method: 'post',
    data: param
  })
}

// 获取系统配置参数MAP K-V
export function properties(param) {
  return request({
    url: 'rest/sysProperty/properties',
    method: 'post',
    data: param
  })
}

// 保存系统参数表
export function saveParam(param) {
  return request({
    url: 'rest/sysProperty/save',
    method: 'post',
    data: param
  })
}

// 刷新生效参数配置
export function refreshPara(param) {
  return request({
    url: 'rest/sysProperty/refresh',
    method: 'post',
    data: param
  })
}

// 根据参数编码获取单条配置
export function propertyPara(param) {
  return request({
    url: 'rest/sysProperty/property',
    method: 'post',
    data: param
  })
}
