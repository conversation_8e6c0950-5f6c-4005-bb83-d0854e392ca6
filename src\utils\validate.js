
// import { Message } from 'element-ui'
/* url:合法URL ,lowerCase 小写 ，upperCase 大写 ，alphabets 大小写，phone 手机号，card 身份证，email 邮箱*/
const validateMap = {
  url: {
    rule: /^(https?|ftp):\/\/([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+\.(com|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[a-zA-Z]{2}))(:[0-9]+)*(\/($|[a-zA-Z0-9.,?'\\+&%$#=~_-]+))*$/,
    name: 'url输入不正确'
  },
  lowerCase: { rule: /^[a-z]+$/, name: '只能为小写字母' },
  upperCase: { rule: /^[A-Z]+$/, name: '只能为大写字母' },
  alphabets: { rule: /^[A-Za-z]+$/, name: '只能为大小写字母' },
  phone: { rule: /^1[0-9]{10}$/, name: '请输入正确手机号' },
  card: { rule: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/, name: '请输入正确的身份证' },
  emial: {
    rule: /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
    name: '请输入正确的邮箱'
  }
}

// 必填项
export function validateRequire(rule, value, callback) {
  if (value === '') {
    callback(rule.message ? rule.message : '该项为必填项')
  } else {
    if (rule.ruleType && rule.ruleType !== '') {
      if (validateMap[rule.ruleType].rule.test(value) === false) {
        callback(validateMap[rule.ruleType].name)
      } else {
        callback()
      }
    } else {
      callback()
    }
  }
}

export function isFileOversize(size) {
  return !isNaN(size) && (size / 1024) / 1024 > 200
}

// 测点配置规则：上限值 > 最大值 > 最小值 > 下限值
export function validateMeasurePointThreshold(form) {
  const list = ['maxLimit', 'maxValue', 'minValue', 'minLimit']
  function isNumber(val) {
    return !isNaN(val) && val !== ''
  }
  function validateCompare(firstIndex, secondIndex) {
    const value1 = form[list[firstIndex]]
    const value2 = form[list[secondIndex]]
    if (!isNumber(value1)) {
      if (firstIndex > 0) {
        return validateCompare(firstIndex - 1, secondIndex)
      }
    }
    if (!isNumber(value2)) {
      if (secondIndex < 3) {
        return validateCompare(firstIndex, secondIndex + 1)
      }
    }
    if (isNumber(value1) && isNumber(value2)) {
      return Number(value1) > Number(value2)
    }
    return true
  }
  let compareValid = true
  for (let i = 0; i < 3; i++) {
    compareValid = compareValid && validateCompare(i, i + 1)
  }
  return compareValid
}
