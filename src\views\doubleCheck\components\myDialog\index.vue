<template>
  <!-- 事件处理 -->
  <el-dialog
    title="事件处理"
    :visible.sync="eventVisible"
    @close="closeEventVisible"
    width="1100px"
  >
    <el-form
      :rules="eventRules"
      :model="eventForm"
      label-position="center"
      label-width="120px"
      ref="form"
    >
      <el-row>
        <el-col :span="12">
          <el-form-item label="识别点位">
            <span>{{ eventForm.deviceName }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="事件分类">
            <span>{{ eventForm.dllptCategory }}</span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="事件类型">
            <span>{{ eventForm.dllptType }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="事件子类型">
            <span>{{ eventForm.dllptSubType }}</span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="事件等级">
            <span>一般</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="事发桩号">
            <span>{{ eventForm.mileage }}</span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="发现时间">
            <span>{{
              eventForm.alertTime | parseTime("{y}-{m}-{d} {h}:{i}:{s}")
            }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="事发车道">
            <span>{{ eventForm.lane }}</span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="现场图片">
            <!-- <img :src="eventForm.imageUrl" style="width:200px;height: 200px;" /> -->
            <el-image
              style="width: 200px; height: 200px"
              :src="eventForm.imageUrl"
              :preview-src-list="imgList"
              fit="cover"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <!-- <el-form-item label="现场视频"> </el-form-item> -->
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="14" style="min-height:1px;"> </el-col>
        <el-col :span="10" v-if="showVideo">
          <div
            class="video-card dashboard-card"
            id="video-card"
            style="width: 300px;"
          >
            <div class="content-wrapper" style="padding:10px 0px;">
              <div class="video-wrapper">
                <div class="video" v-loading="videoLoading">
                  <!-- :src="videoSrc" -->
                  <video
                    :src="videoUrl"
                    muted
                    controls
                    id="video"
                    width="300px"
                    height="224px"
                  ></video>
                </div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
      <el-row v-if="!isEdit">
        <el-col :span="24">
          <el-form-item label="是否属实">
            <span>{{
              eventForm.checkResult
                ? eventForm.checkResult === 1
                  ? "属实"
                  : eventForm.checkResult === -1
                    ? "误报"
                    : ""
                : ""
            }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="24" v-if="eventForm.checkTime">
          <el-form-item label="审核时间">
            <span>{{
              eventForm.checkTime | parseTime("{y}-{m}-{d} {h}:{i}:{s}")
            }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="24" v-if="eventForm.userName">
          <el-form-item label="审核人员">
            <span>{{ eventForm.userName }}</span>
          </el-form-item>
        </el-col>
      </el-row>
      <div v-if="isEdit">
        <el-row>
          <el-col :span="24">
            <el-form-item label="处置流程"> </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col class="radio-group" :span="24">
            <div>
              <span>请确认该事件是否属实</span>
              <el-form-item label="情况属实:" prop="checkResult">
                <el-radio-group v-model="eventForm.checkResult">
                  <el-radio :label="1">通过</el-radio>
                  <el-radio :label="-1">不通过</el-radio>
                </el-radio-group>
              </el-form-item>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-form>

    <div class="btnDiv">
      <el-button
        type="primary"
        v-if="isEdit"
        :loading="eventLoading"
        @click="checkResultChange"
      >提交</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import { confirmEmergencyApi } from '@/api/index.js'
import AutoUpload from '@/components/AutoUpload'
// import { getNameByCode, parseTime } from '@/utils'
import waves from '@/directive/waves'
import { docApi } from '@/api/index.js'
export default {
  name: 'MyDialog',
  components: {
    AutoUpload
  },
  directives: { waves },
  props: {
    eventVisible: {
      type: Boolean,
      default: false
    },
    // 单据操作状态：false--查看 true--编辑
    isEdit: {
      type: Boolean,
      default: false
    },
    showId: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      eventRules: {
        checkResult: [
          { required: true, message: '请选择情况是否属实', trigger: 'change' }
        ]
      },
      eventForm: {},
      eventLoading: false,
      showIsReported: '',
      showVideo: false,
      imgList: []
    }
  },
  computed: {},
  mounted() {},
  unmounted() {
    this.loading = false
  },
  methods: {
    // 关闭事件处理弹窗
    openDialog(id) {
      this.getconfirmEmergency(id)
      this.eventVisible = true
    },
    closeEventVisible() {
      this.eventVisible = false
      this.$emit('onChange', false)
    },
    checkResultChange(id) {
      this.$refs.form.validate(valid => {
        console.log(valid)
        if (valid) {
          this.eventLoading = true
          this.showIsReported = this.eventForm.checkResult
          confirmEmergencyApi
            .confirm({ id: this.showId, check_result: this.showIsReported })
            .then(res => {
              if (res.success) {
                console.log(res.result, 'res.result-confirm')
                this.$emit('saveSuccess')
              }
              this.eventLoading = false
              this.eventVisible = false
            })
            .catch(() => {
              this.eventLoading = false
            })
        }
      })
      return
    },
    async getconfirmEmergency(id) {
      const res = await confirmEmergencyApi.get({ id })
      if (res.success) {
        console.log(res.result, 'res.result-get')
        this.eventForm = res.result
        console.log(this.eventForm, 'res.result-this.eventForm')

        // 获取
        const { imageId } = res.result
        if (imageId) {
          docApi.attachList({ docId: imageId }).then(res => {
            console.log('图片信息', res)
            if (res.success && res.result && res.result.length > 0) {
              this.eventForm.imageUrl =
                'http://***********:10000/stec-platform-doc/img/' +
                res.result[0].fileName
              this.imgList = [this.eventForm.imageUrl]
            }
            console.log('this.myUrl', this.myUrl)
          })
        }
      }
    }
  }
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.double-check-container {
  /deep/.mxCard .titleSpan {
    margin-left: -30px;
  }
  .filter-container {
    margin-left: 40px;
  }
  .label-name {
    font-size: 14px;
    position: relative;
    top: -5px;
  }
  .vue-treeselect {
    height: 32px;
  }
  .imgCol {
    img {
      width: 148px;
      height: 148px;
      margin-right: 10px;
    }
  }
  .radio-group {
    margin-left: 50px;
    width: 350px;
    display: flex;
    justify-content: center;
    text-align: center;
    flex-direction: column;
  }
}
.btnDiv {
  margin-top: 20px;
  text-align: center;
}
</style>
<style>
.el-image-viewer__wrapper {
  z-index: 99999 !important;
}
</style>
