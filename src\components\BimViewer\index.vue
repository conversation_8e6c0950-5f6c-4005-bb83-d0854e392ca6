<template>
  <div
    ref="webgl"
    id="webgl"
    style="position: absolute;z-index: 999;left: 0; top: 0;"
    :style="{width: width, height: height}"
  ></div>
</template>
<script>
import { mapGetters } from 'vuex'
/**
 * Bimrun 模型调用展示组件
 * @module BimViewer
 */
export default {
  name: 'BimViewer',
  /**
   * Props
   * @prop {Object} currentCameraInfo 视点信息
   * @prop {String} bimProjectId 项目id
   * @prop {String} bimSceneId 场景id
   * @prop {Number|String} selectModel 选中模型id
   * @prop {Boolean} isFullScreen 是否全屏，默认false
   */
  props: {
    currentCameraInfo: {
      type: Object,
      default: () => {}
    },
    bimProjectId: {
      type: String,
      default: ''
    },
    bimSceneId: {
      type: String,
      default: ''
    },
    sceneList: {
      type: Array,
      default: () => []
    },
    selectedModel: {
      type: Number | String,
      default: null
    },
    isFullScreen: {
      type: Boolean,
      default: false
    },
    viewH: {
      type: String,
      default: '302px'
    },
    viewW: {
      type: String,
      default: '595px'
    }
  },
  data() {
    return {
      width: this.viewW || '',
      height: this.viewH || '',
      isFull: false,
      selectedColor: null,
      transparentColor: null,
      // sceneList: [this.bimSceneId],
      loadingTarget: null,
      loadingOption: null
    }
  },
  computed: {
    ...mapGetters([
      'viewer'
    ])
  },
  watch: {
    isFullScreen(val) {
      if (val) {
        this.screenWidth = document.documentElement.clientWidth
        this.screenHeight = document.documentElement.clientHeight
        this.width = `${this.screenWidth - 180}px`
        this.height = `${this.screenHeight - 140}px`
        if (this.viewer) {
          this.$nextTick(() => {
            if (document.getElementById('viewer')) {
              document.getElementById('viewer').style.width = '100%'
              document.getElementById('viewer').style.height = '100%'
              document.getElementById('viewer').width = this.screenWidth - 180
              document.getElementById('viewer').height = this.screenHeight - 140
              this.viewer.resizeWindow()
              this.viewer.change()
            }
          })
        }
      } else {
        this.width = '100%'
        this.height = '100%'
        if (this.viewer) {
          this.$nextTick(() => {
            if (document.getElementById('viewer')) {
              document.getElementById('viewer').style.width = '100%'
              document.getElementById('viewer').style.height = '100%'
              this.viewer.resizeWindow()
              this.viewer.change()
            }
          })
        }
      }
    },
    selectedModel(val) {
      if (!val) {
        return
      }
      this.updateSelectModel()
    },
    currentCameraInfo(val) {
      if (!val) {
        return
      }
      this.changeCamera(this.currentCameraInfo)
    },
    viewH() {
      this.height = this.viewH
      this.updateSize()
    },
    viewW() {
      this.width = this.viewW
      this.updateSize()
    }
  },
  created() {

  },
  mounted() {
    // 模型颜色
    this.transparentColor = new THREE.Color(0.1, 0.1, 0.1)
    this.selectedColor = new THREE.Color(1, 0, 0)
    // 模型loading
    this.loadingTarget = document.getElementById('webgl')
    this.loadingOption = {
      target: this.loadingTarget,
      lock: true,
      text: '正在加载，请等待......',
      spinner: 'el-icon-loading',
      background: 'rgba(0, 0, 0, 0.7)'
    }
    // 获取场景列表
    // this.sceneList = JSON.parse(this.$storage.getStorage('project')).modelUrl.split(',')
    this.$nextTick(() => {
      this.initWebgl()
    })
  },
  activated() {
    if (this.viewer) {
      document.getElementById('webgl').append(this.viewer.canvas)
      this.changeCamera(this.currentCameraInfo)
    }
  },
  methods: {
    async initWebgl() {
      const viewer = await this.initViewer()
      if (viewer) {
        this.changeCamera(this.currentCameraInfo)
        this.$emit('getViewer')
      }
    },
    initViewer() {
      if (this.viewer) {
        document.getElementById('webgl').append(this.viewer.canvas)
        this.viewer.change()
        return new Promise(resolve => {
          resolve(this.viewer)
        })
      } else {
        return new Promise(resolve => {
          BIMRUN.parameter.selectedColor = 0x0000EE
          const element = document.getElementById('webgl')
          const newViewer = new JY.THREE.Viewer({
            container: element
          })
          this.$store.dispatch('setViewer', newViewer)
          // 加载模型
          const isLoadByProjectId = false
          if (isLoadByProjectId) {
            newViewer.loadProject('196425543')
          } else {
            // newViewer.loadScene(this.bimSceneId)
            this.sceneList.forEach(id => {
              newViewer.loadScene(id)
            })
          }
          let num = 0
          const sceneNum = this.sceneList.length
          // const loading = this.$loading(this.loadingOption)
          newViewer.listen(JY.THREE.Events.ManifestLoaded, function() {
            if (isLoadByProjectId) {
              // loading.close()
              resolve(newViewer)
            } else {
              if (++num === sceneNum) {
                // loading.close()
                resolve(newViewer)
              }
            }
          })
        })
      }
    },
    updateSelectModel() {
      if (!this.selectedModel || !this.viewer) {
        return
      }
      const model = this.viewer.getModelById(this.selectedModel)
      if (!model) {
        return
      }
      this.viewer.flyToModel(model)
      this.viewer.clearSelection()
      this.viewer.addSelection([model])
      // this.setColor()
    },
    /**
     * 调整视点
     * @function changeCamera
     * @param cameraInfo {Object} - 视点信息，eg.{position: {0, 10, 10}, target: {0, 0, 0}}
     */
    changeCamera(cameraInfo) {
      if (!this.viewer || !cameraInfo) {
        return
      }
      this.viewer.flyTo(cameraInfo)
      this.setColor()
    },
    setColor() {
      const allModelList = this.viewer.filterModels(v => v)
      this.viewer.setModelsCustomColor(allModelList, this.transparentColor, 0.1)
      let model = null
      if (this.selectedModel) {
        model = this.viewer.getModelById(this.selectedModel)
      }
      if (model) {
        this.viewer.setModelsCustomColor([model], this.selectedColor, 1)
      }
    },
    updateSize() {
      this.$nextTick(() => {
        this.viewer.resizeWindow()
        this.viewer.change()
      })
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss"  scoped>
#webgl {
  /deep/ #BimrunViewer {
    outline: none;
  }
}
</style>
