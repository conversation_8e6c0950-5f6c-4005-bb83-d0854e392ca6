import request from '@/utils/request'

export function getChannelAlertList(param) {
  return request({
    url: 'rest/channelAlert/list',
    method: 'post',
    data: param
  })
}

export function countAlertByTime(param) {
  return request({
    url: 'rest/channelAlert/countAlertByTime',
    method: 'post',
    data: param
  })
}

export function deleteChannelAlert(param) {
  return request({
    url: 'rest/channelAlert/delete',
    method: 'post',
    data: param
  })
}

export function saveChannelAlert(param) {
  return request({
    url: 'rest/channelAlert/save',
    method: 'post',
    data: param
  })
}

export function getChannelAlert(param) {
  return request({
    url: 'rest/channelAlert/get',
    method: 'post',
    data: param
  })
}

export function getChannelCount(param) {
  return request({
    url: 'rest/channelAlert/count',
    method: 'post',
    data: param
  })
}

export function getChannelCountAlert(param) {
  return request({
    url: 'rest/channelAlert/countAlert',
    method: 'post',
    data: param
  })
}

export function getData(param) {
  return request({
    url: 'rest/data/getData',
    method: 'post',
    data: param
  })
}

export function saveChannalAlertInspect(param) {
  return request({
    url: 'rest/channelAlertInspect/inspect',
    method: 'post',
    data: param
  })
}

export function getChannalAlertInspect(param) {
  return request({
    url: 'rest/channelAlertInspect/list',
    method: 'post',
    data: param
  })
}

// 导出报警列表
export function exportAlertList(param) {
  return request({
    url: 'rest/channelAlertInspect/exportAlertList',
    method: 'post',
    data: param
  })
}

