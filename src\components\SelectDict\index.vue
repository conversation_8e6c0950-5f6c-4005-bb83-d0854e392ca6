<!-- 选择器 用于字典字段-->
<template>
  <el-select
    :disabled="disabled"
    :style="{width: width + 'px'}"
    v-model="code"
    :placeholder="`请选择${placeText}`"
    :multiple="multiple"
    @input="handleInput"
    filterable
    :clearable="clearable">
    <template v-for="item in dictMap[dictName]">
      <el-option
        v-if="isOptionValid(item)"
        :key="item.code"
        :label="item.name"
        :value="item.code">
      </el-option>
    </template>
  </el-select>
</template>

<script>
  /**
   * 字典选择器 SelectDict
   * @module SelectDict
   */
  import { mapGetters } from 'vuex'
  export default {
    name: 'SelectDict',
    model: {
      prop: 'currentCode',
      event: 'change'
    },
    /**
     * Props 参数
     * @prop {String|Array} currentCode v-model 绑定值
     * @prop {String} dictName 字典父级Code
     * @prop {String} placeText placeholder文字
     * @prop {Number} width 宽度
     * @prop {Boolean} disabled 是否禁用
     * @prop {Array} removeItems 移除项
     * @prop {Boolean} multiple 是否多选
     */
    props: {
      currentCode: {
        type: String | Array,
        default: ''
      },
      dictName: {
        type: String,
        default: ''
      },
      placeText: {
        type: String,
        default: ''
      },
      width: {
        type: Number | String,
        default: 200
      },
      disabled: {
        type: Boolean,
        default: false
      },
      removeItems: {
        type: Array,
        default: () => []
      },
      codeList: {
        type: Array,
        default: () => []
      },
      multiple: {
        type: Boolean,
        default: false
      },
      clearable: {
        type: Boolean,
        default: true
      }
    },
    data() {
      return {
        code: this.currentCode,
        configItems: [] // 手动设置
      }
    },
    computed: {
      ...mapGetters([
        'dictMap'
      ])
    },
    watch: {
      code() {
        this.$emit('change', this.code)
      },
      currentCode() {
        this.code = this.currentCode
        // this.$emit('change', this.code)
      }
    },
    mounted() {
      if (this.codeList.length > 0) {
        this.configItems = [...this.codeList]
      }
    },
    methods: {
      isOptionValid(item) {
        const notRemove = this.removeItems.indexOf(item.code) === -1
        const isConfig = this.configItems.length > 0
        const inConfig = this.configItems.indexOf(item.code) !== -1
        return (isConfig && inConfig) || (!isConfig && notRemove)
      },
      handleInput(val) {
        this.$emit('input', val)
      }
    }
  }
</script>
