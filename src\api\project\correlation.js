import request from '@/utils/request'

export function getCorrelationList(param) {
  return request({
    url: 'rest/correlation/list',
    method: 'post',
    data: param
  })
}

export function saveCorrelationList(param) {
  return request({
    url: 'rest/correlation/save',
    method: 'post',
    data: param
  })
}

export function deleteCorrelationList(param) {
  return request({
    url: 'rest/correlation/delete',
    method: 'post',
    data: param
  })
}

export function getCorrelationResultList(param) {
  return request({
    url: 'rest/correlation/listResult',
    method: 'post',
    data: param
  })
}

export function getCorrelationResult(param) {
  return request({
    url: 'rest/correlation/getResult',
    method: 'post',
    data: param
  })
}

// 相关性分析结果
export function analysisResult(param) {
  return request({
    url: 'rest/correlation/analysisResult',
    method: 'post',
    data: param
  })
}
