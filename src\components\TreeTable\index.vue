<template>
  <el-table :data="formatData"
            :row-style="showRow"
            :show-header="showHeader"
            v-bind="$attrs"
            size="small"
            v-loading="listLoading"
            empty-text="暂无数据"
            element-loading-text="加载中..."
            :header-row-style="{color: '#000',height: '34px'}"
            :row-class-name="tableRowClassName"
            style="width: 100%;border: 0px;">
    <el-table-column v-if="columns.length===0"
                     width="150">
      <template slot-scope="scope">
        <span v-for="space in scope.row._level"
              class="ms-tree-space"
              :key="space"></span>
        <span class="tree-ctrl"
              v-if="iconShow(0,scope.row)"
              @click="toggleExpanded(scope.$index)">
          <i v-if="!scope.row._expanded"
             class="el-icon-plus"></i>
          <i v-else
             class="el-icon-minus"></i>
        </span>
        {{ scope.$index }}
      </template>
    </el-table-column>
    <el-table-column v-for="(column, index) in columns"
                     :key="column.value"
                     :label="column.text"
                     :width="column.width"
                     align="left"
                     v-if="column.type!='button'&&column.type!='iconButton'&&column.type!='textButton'&&column.type!='switch'&&column.type!=='checkbox'&&columns.length>0">
      <my-tree-column v-for="(child,index) in column.children"
                      :key="index"
                      :item="child"
                      v-if="column.children"></my-tree-column>
      <template slot-scope="scope">
        <span v-if="index === 0"
              v-for="space in scope.row._level"
              class="ms-tree-space"
              :key="space"></span>
        <span class="tree-ctrl"
              v-if="iconShow(index,scope.row)"
              @click="toggleExpanded(scope.$index)">
          <i v-if="!scope.row._expanded"
             class="el-icon-plus"></i>
          <i v-else
             class="el-icon-minus"></i>
        </span>
        <span
          v-html="formatter(scope.row[column.value],column.formatter,scope.row,column.filter,column.filterParams)"
          v-bind:class="column.classFun?column.classFun(scope.row):''">
        </span>
      </template>
    </el-table-column>

    <!--<el-table-column v-for="(column, index) in columns" :key="column.value" :formatter='column.formatter' :label="column.text" :width="column.width" v-if="column.type==='text'&&columns.length>0">-->

    <!--</el-table-column>-->

    <el-table-column v-for="(column) in columns"
                     :key="column.value"
                     :label="column.text"
                     :width="column.width"
                     align="center"
                     v-if="column.type==='button'&&columns.length>0">
      <template slot-scope="scope">
        <el-button v-if="column.type==='button'"
                   size="mini"
                   @click="key.click(scope,$event)"
                   v-for="(key, num) in column.list(scope.row)"
                   :key="num"
                   :type="key.type">{{ key.value }}
        </el-button>
      </template>
    </el-table-column>

    <el-table-column v-for="(column) in columns"
                     :key="column.value"
                     :label="column.text"
                     :width="column.width"
                     align="center"
                     v-if="(column.type==='iconButton' || column.type==='textButton' || column.type==='switch' || column.type === 'checkbox')&&columns.length>0">
      <template slot-scope="scope">
        <div v-if="column.type === 'iconButton'">
          <span v-for="(key, num) in dealBtnsByPrivilege(column.list(scope.row))"
                :key="num">
            <!-- v-button-privilege="key.privilege" -->
            <el-tooltip :content="key.value"
                        placement="top">
              <i style="font-size: 25px;"
                 class="icon iconfont pointer projectColor"
                 :class="key.class"
                 @click="key.click(scope,$event)"></i>
            </el-tooltip>
            <!--<i v-button-privilege="key.privilege" style="font-size: 25px; padding-right: 5px;" class="icon iconfont projectColor pointer" :class="key.class" @click='key.click(scope,$event)'  ></i>-->
            <span class="projectColor"
                  style="position: absolute;margin-left: -4px;font-weight: 700;opacity: 0.5;"
                  v-if="num < dealBtnsByPrivilege(column.list(scope.row)).length-1">|</span>
          </span>
        </div>
        <div v-if="column.type === 'textButton'" class="textButton">
          <el-button class="pointer textSpan"
                     type="text"
                     v-button-privilege="key.privilege"
                     @click="key.click(scope,$event)"
                     :key="num"
                     v-for="(key, num) in dealBtnsByPrivilege(column.list(scope.row))">{{ key.value }}
            <span style="margin: 5px;color: rgba(0,0,0,0.3)" v-if="num < dealBtnsByPrivilege(column.list(scope.row)).length-1">|</span>
          </el-button>
        </div>
        <div v-if="column.type === 'switch'">
          <el-switch v-model="scope.row[column.model]"
                     v-if="column.show(scope.row)"
                     :disabled="column.disabled"
                     @change="column.change(scope,scope.row[column.model])"
                     :active-value="column.activeValue"
                     :inactive-value="column.inactiveValue"
                     :active-text="column.activeText"
                     :inactive-text="column.inactiveText"/>
        </div>
        <div v-if="column.type === 'checkbox'">
          <el-checkbox
            v-model="scope.row[column.model]"
            v-if="column.show(scope.row)"
            :disabled="column.disabled"
            @change="column.change(scope,scope.row[column.model])"
          />
        </div>
      </template>
    </el-table-column>
    <slot></slot>
  </el-table>
</template>

<script>
import treeToArray from './eval'
import buttonPrivilege from '@/directive/buttonPrivilege'
import myTreeColumn from './myTreeColumn'
import { mapGetters } from 'vuex'
export default {
  name: 'TreeTable',
  directives: {
    buttonPrivilege
  },
  components: {
    myTreeColumn
  },
  props: {
    data: {
      type: [Array, Object],
      required: true
    },
    columns: {
      type: Array,
      default: () => []
    },
    evalFunc: {
      type: Function,
      default: function() {
      }
    },
    evalArgs: {
      type: Array,
      default: () => []
    },
    expandAll: {
      type: Boolean,
      default: false
    },
    showHeader: {
      type: Boolean,
      default: true
    },
    listLoading: { type: Boolean, default: false },
    tableRowClassName: {
      type: Function,
      default() {
        return () => {}
      }
    }
  },
  computed: {
    // 格式化数据源
    formatData: function() {
      let tmp
      if (!Array.isArray(this.data)) {
        tmp = [this.data]
      } else {
        if (this.data.length > 0) {
          // 组装参数
          tmp = this.treeListUtil(this.data)
        } else {
          tmp = this.data
        }
      }
      const func = treeToArray
      const args = this.evalArgs ? Array.concat([tmp, this.expandAll], this.evalArgs) : [tmp, this.expandAll]
      return func.apply(null, args)
    },
    ...mapGetters(['projectButtonPrivileges', 'buttonPrivileges'])
  },
  methods: {
    showRow: function(row) {
      const show = (row.row.parent ? (row.row.parent._expanded && row.row.parent._show) : true)
      row.row._show = show
      return show ? { animation: 'treeTableShow 1s', '-webkit-animation': 'treeTableShow 1s' } : { visibility: 'collapse' }
    },
    // 数据组织
    treeListUtil(data, parentId) {
      const itemArr = []
      for (let i = 0; i < data.length; i++) {
        const node = data[i]
        if (node.parentId === parentId) {
          node.children = this.treeListUtil(data, node.id)
          itemArr.push(node)
        }
      }
      return itemArr
    },
    // 切换下级是否展开
    toggleExpanded: function(trIndex) {
      const record = this.formatData[trIndex]
      record._expanded = !record._expanded
      this.$emit('loadMoreData', record)
    },
    // 图标显示
    iconShow(index, record) {
      return ((index === 0 && record.children && record.children.length > 0) || (index === 0 && record.leaf === false))
    },
    formatter(value, formatter, row, filter, filterParams) {
      if (!formatter) {
        if (filter) {
          if (filterParams) {
            const tempArr = [value, ...filterParams]
            return filter(...tempArr)
          } else {
            return filter(value)
          }
        }
        return value
      } else {
        return formatter(row)
      }
    },
    /** *
 * 解决问题  如果是有权限时 利用指令的话  最后一条竖线是始终存在的
 * 解决方案 将有权限的按钮提前筛选出来
 * by zhujun
 */
    dealBtnsByPrivilege(btnList) {
      let iconBtnList = []
      let prop = 'buttonPrivileges'
      if (this.$storage.getStorage('project')) {
        prop = 'projectButtonPrivileges'
      }
      iconBtnList = btnList.filter(item => {
        return this[prop].indexOf(item.privilege) !== -1 || !item.privilege
      })
      return iconBtnList
    }
  }
}
</script>
<style rel="stylesheet/css">
@keyframes treeTableShow {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@-webkit-keyframes treeTableShow {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
</style>

<style lang="scss" rel="stylesheet/scss" scoped>
$color-blue: #2196f3;
$space-width: 18px;
.ms-tree-space {
  position: relative;
  top: 1px;
  display: inline-block;
  font-style: normal;
  font-weight: 400;
  line-height: 1;
  width: $space-width;
  height: 14px;
  &::before {
    content: '';
  }
}

.processContainer {
  width: 100%;
  height: 100%;
}

table td {
  line-height: 26px;
}

.tree-ctrl {
  position: relative;
  cursor: pointer;
  color: $color-blue;
  margin-left: -$space-width;
}
.table-row-title {
  text-align: left;
  border: 1px red solid;
}
.textButton {
  text-align: right;
}
</style>
