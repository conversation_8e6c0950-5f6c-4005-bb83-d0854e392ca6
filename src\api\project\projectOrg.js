import request from '@/utils/request'

// 配置单项目项目组织结构树
export function treeData(param) {
  return request({
    url: 'rest/projectOrg/treeData',
    method: 'post',
    data: param
  })
}

// 配置单项目项目组织结构树
export function getProjectOrgDetail(param) {
  return request({
    url: 'rest/projectOrg/detail',
    method: 'post',
    data: param
  })
}

// 保存项目组织
export function saveProjectOrg(param) {
  return request({
    url: 'rest/projectOrg/save',
    method: 'post',
    data: param
  })
}
