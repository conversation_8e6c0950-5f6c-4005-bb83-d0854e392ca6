<template>
  <treeselect
    :options="deviceSystemTreeList"
    no-children-text="无选择"
    placeholder="请选择测点类型"
    :normalizer="normalizer"
    :multiple="multiple"
    v-model="ids"
    :disabled="disabled"
    no-options-text="正在加载..."
    @input="handleInput"
    :clearable="clearable"
    :disable-branch-nodes="disableBranchNodes"
  ></treeselect>
</template>

<script>
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
// import { getDeviceCategoryList } from '@/api/project/deviceSystem'
// import { deviceExistSystemList } from '@/api/system/device'
import { getDeviceTypeList } from '@/api/system/device'
import { deviceTypeListByDevice, deviceTypeListByDeviceGroup } from '../../api/project/deviceTypeDiy'
/**
 * 选择设备分类组件
 * @module DeviceCategory
 */
export default {
  name: 'SelectDeviceType',
  /**
   * @prop {Component} Treeselect 树形选择组件
   */
  components: { Treeselect },
  model: {
    prop: 'categoryId',
    event: 'change'
  },
  /**
   * Props
   * @prop {Number|Array} categoryId v-model绑定值，设备分类Id，默认 null
   * @prop {Boolean} multiple 是否多选，默认 false
   * @prop {Boolean} disableBranchNodes 是否禁用非子节点，默认 false
   * @prop {Boolean} filterExist 是否过滤掉无设备的类型, 默认 false
   * @prop {Boolean} isMonitorSystem 是否是监测设备类型，默认 false
   * @prop {Boolean} deviceCategoryDisabled 是否禁用选择器
   * @prop {String} filter 过滤条件，默认default 全部类型，device 有设备的类型树，'deviceGroup' 有设备组的类型树
   */
  props: {
    categoryId: {
      type: Number | Array,
      default: null
    },
    multiple: {
      type: Boolean,
      default: false
    },
    disableBranchNodes: {
      type: Boolean,
      default: false
    },
    filterExist: {
      type: Boolean,
      default: false
    },
    isMonitorSystem: {
      type: Boolean,
      default: true
    },
    deviceCategoryDisabled: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    clearable: {
      type: Boolean,
      default: true
    },
    filter: {
      type: String,
      default: 'default'
    }
  },
  data() {
    return {
      ids: this.categoryId,
      deviceSystemList: [],
      deviceSystemTreeList: [],
      normalizer(node) {
        return {
          id: node.id,
          label: node.name,
          children: node.children
        }
      },
      getData: false
    }
  },
  computed: {
    dataReady() {
      return this.deviceSystemList && this.deviceSystemList.length > 0 && this.getData
    }
  },
  watch: {
    ids() {
      this.$emit('change', this.ids)
      this.emitTypes()
    },
    categoryId() {
      this.ids = this.categoryId ? this.categoryId : null
    },
    getData(val) {
      if (!val) {
        return
      }
      this.emitTypes()
    }
  },
  mounted() {
    this.getDeviceSystemList()
    if (this.ids) {
      if (!this.multiple && this.ids.indexOf) {
        this.ids = this.ids[0] || null
      }
    }
  },
  methods: {
    getDeviceSystemList() {
      // let method = this.isMonitorSystem ? getDeviceTypeList : getDeviceCategoryList
      // if (!this.isMonitorSystem) {
      //   method = this.filterExist ? deviceExistSystemList : getDeviceCategoryList
      // }
      const info = this.filterExist ? {
        currPage: 1,
        pageSize: 2147483647,
        params: { type: 'device' }
      } : {
        currPage: 1,
        pageSize: 2147483647,
        params: {}
      }
      const method = {
        default: getDeviceTypeList,
        device: deviceTypeListByDevice,
        deviceGroup: deviceTypeListByDeviceGroup
      }[this.filter]
      method(info).then(response => {
        if (response.success) {
          this.deviceSystemList = this.isMonitorSystem ? response.result.list : response.result
          if (this.filter === 'deviceGroup') {
            this.deviceSystemList = response.result
          }
          this.deviceSystemTreeList = this.treeListUtil(this.deviceSystemList)
          this.getData = true
        }
      })
    },
    treeListUtil(data, parentId) {
      const itemArr = []
      for (let i = 0; i < data.length; i++) {
        const node = data[i]
        if (node.parentId === parentId) {
          if (this.treeListUtil(data, node.id).length > 0) {
            node.children = this.treeListUtil(data, node.id)
          }
          itemArr.push(node)
        }
      }
      return itemArr
    },
    emitTypes() {
      if (this.multiple && this.ids && this.ids.indexOf) {
        const selectList = this.deviceSystemList.filter(item => (this.ids.indexOf(item.id) !== -1) || (this.ids.indexOf(item.id.toString()) !== -1))
        this.$emit('getTypes', selectList)
      } else {
        const selectList = this.deviceSystemList.find(item => item.id.toString() === this.ids)
        this.$emit('getTypes', selectList)
      }
    },
    handleInput(val) {
      this.$emit('input', val)
    }
  }
}
</script>

<style scoped>
</style>
