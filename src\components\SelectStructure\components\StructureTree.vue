<template>
  <treeselect
    v-model="id"
    ref="test"
    class="filter-item"
    style="width: 300px"
    :options="structureList"
    :normalizer="normalizer"
    placeholder="请选择设施结构"
    @select="structureUpdate"
    :load-options="loadOptions"
    no-children-text="无选择"
  >
    <label
      slot="option-label"
      slot-scope="{
        node,
        shouldShowCount,
        count,
        labelClassName,
        countClassName,
      }"
      :class="labelClassName"
      :title="node.label"
    >
      {{ node.label }}
    </label>
  </treeselect>
</template>

<script>
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import { structureTreeData } from '@/api/project/project'
import { aysnTree } from '@/views/mixin'
import { getProjectList } from '@/api/project/project'

export default {
  name: 'StructureTree',
  components: { Treeselect },
  mixins: { aysnTree },
  model: {
    prop: 'structureId',
    event: 'change'
  },
  props: {
    structureId: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      id: this.structureId,
      structureList: [],
      normalizer(node) {
        return {
          id: node.id,
          label: node.name,
          children: node.children
        }
      }
    }
  },
  watch: {
    id() {
      this.$emit('change', this.id)
    },
    structureId() {
      this.id = this.structureId
      this.$emit('change', this.structureId)
    }
  },
  mounted() {
    this.initStructure()
  },
  methods: {
    structureUpdate() {
    },
    // 获取项目
    getProjectList() {
      return new Promise((resolve) => {
        setTimeout(() => {
          getProjectList({
            currPage: 1,
            pageSize: 2147483647,
            params: {}
          }).then(response => {
            if (response.success) {
              resolve(response.result.list)
            } else {
              resolve([])
            }
          })
        })
      })
    },
    async initStructure() {
      const projectList = await this.getProjectList()
      if (projectList.length === 0) {
        return
      }
      const projectId = JSON.parse(this.$storage.getStorage('project')).id || projectList[0].id
      const projectName = JSON.parse(this.$storage.getStorage('project')).name || projectList[0].name
      const first = await this.getStructure(projectId)
      this.structureList = [{
        id: 'project' + projectId,
        name: projectName,
        children: first,
        type: 'project'
      }]
    },
    // 获取结构
    getStructure(projectId) {
      return new Promise((resolve) => {
        setTimeout(() => {
          structureTreeData({ id: projectId, type: 'project' }).then(response => {
            if (response.success) {
              response.result.forEach(item => {
                if (!item.leaf) {
                  item.children = null
                }
              })
              resolve(response.result)
            }
          })
        })
      })
    },
    loadOptions({ action, parentNode, callback }) {
      if (parentNode.children && parentNode.children.length > 0) {
        return
      }
      let id = parentNode.id
      if (parentNode.type === 'project') {
        id = id.substr(7, id.length)
      }
      structureTreeData({ id: id, type: parentNode.type }).then(response => {
        if (response.success) {
          response.result.forEach(item => {
            if (!item.leaf) {
              item.children = null
            }
          })
          parentNode.children = response.result
          callback()
        }
      })
    }
  }
}
</script>

<style scoped>
</style>
