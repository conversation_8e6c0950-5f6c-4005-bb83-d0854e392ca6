<!-- description -->
<template>
  <div class="dp-list-basic">
    <div class="list-item" v-for="(item, index) in list" :key="index">
      <span @click.stop="handleClick(item)">{{ item.name }}</span>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'ListBasic',
    components: {},
    directives: {},
    props: {
      list: {
        type: Array,
        default: () => []
      }
    },
    data() {
      return {}
    },
    computed: {},
    created() {},
    mounted() {},
    methods: {
      handleClick(item) {
        this.$emit('click', item)
      }
    }
  }
</script>

<style rel="stylesheet/scss" lang="scss">
.dp-list-basic {
  width: 100%;
  box-shadow: inset 0px -1px 2px rgba(0, 0, 0, 0.08), inset 0px 2px 4px rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  background: #EEF0F2;
  padding: 0 10px;
  overflow-y: auto;
  .list-item {
    height: 36px;
    line-height: 36px;
    border-bottom: 1px dashed #90A4AE;
    font-size: 14px;
    color: rgba(0, 83, 177, 1);
    &:last-child {
      border: none;
    }
    span {
      text-decoration: underline;
      cursor:pointer;
    }
  }
}
</style>
